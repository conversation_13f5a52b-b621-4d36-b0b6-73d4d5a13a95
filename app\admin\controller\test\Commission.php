<?php

namespace app\admin\controller\test;

use app\common\basics\AdminBase;
use app\common\logic\CommissionLogic;
use app\common\server\JsonServer;
use think\facade\Db;

/**
 * 佣金计算测试控制器
 */
class Commission extends AdminBase
{
    /**
     * 测试页面
     */
    public function index()
    {
        return view('', [
            'title' => '佣金计算功能测试'
        ]);
    }

    /**
     * 测试佣金计算
     */
    public function test()
    {
        try {
            $results = [];

            // 测试商品类型识别
            $goods_list = Db::name('goods')
                ->field('id,name,join_jc')
                ->where('del', 0)
                ->limit(5)
                ->select()
                ->toArray();

            foreach ($goods_list as $goods) {
                $goods_type = CommissionLogic::getGoodsType($goods['id']);
                $type_desc = CommissionLogic::getGoodsTypeDesc($goods_type);
                $commission_ratio = CommissionLogic::getCommissionRatio($goods_type);

                // 计算100元商品的佣金
                $commission_info = CommissionLogic::calculateGoodsCommission($goods['id'], 100.00);

                $results['goods'][] = [
                    'id' => $goods['id'],
                    'name' => $goods['name'],
                    'join_jc' => $goods['join_jc'],
                    'goods_type' => $goods_type,
                    'type_desc' => $type_desc,
                    'commission_ratio' => $commission_ratio,
                    'commission_amount' => $commission_info['commission_amount'],
                    'merchant_amount' => $commission_info['merchant_amount']
                ];
            }

            // 测试订单佣金计算
            $orders = Db::name('order')
                ->field('id,order_sn,order_amount')
                ->where('pay_status', 1)
                ->limit(3)
                ->select()
                ->toArray();

            foreach ($orders as $order) {
                $order_commission = CommissionLogic::calculateOrderCommission($order['id']);
                
                $results['orders'][] = [
                    'id' => $order['id'],
                    'order_sn' => $order['order_sn'],
                    'order_amount' => $order['order_amount'],
                    'total_commission' => $order_commission['total_commission'],
                    'total_merchant_amount' => $order_commission['total_merchant_amount'],
                    'goods_count' => count($order_commission['goods_details'])
                ];
            }

            // 测试配置获取
            $results['config'] = [
                'normal_goods_commission_ratio' => CommissionLogic::getCommissionRatio(CommissionLogic::GOODS_TYPE_NORMAL),
                'jcai_goods_commission_ratio' => CommissionLogic::getCommissionRatio(CommissionLogic::GOODS_TYPE_JCAI),
                'jcai_crowdfunding_commission_ratio' => CommissionLogic::getCommissionRatio(CommissionLogic::GOODS_TYPE_JCAI_CROWDFUNDING)
            ];

            return JsonServer::success('测试完成', $results);

        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试退款佣金调整
     */
    public function testRefundCommission()
    {
        $order_id = $this->request->post('order_id');
        $refund_amount = $this->request->post('refund_amount', 0);

        if (!$order_id) {
            return JsonServer::error('请提供订单ID');
        }

        try {
            // 计算原始佣金
            $original_commission = CommissionLogic::calculateOrderCommission($order_id, false);

            // 计算考虑退款后的佣金
            $adjusted_commission = CommissionLogic::calculateOrderCommission($order_id, true);

            // 计算退款对佣金的影响
            $adjustment_info = CommissionLogic::calculateRefundCommissionAdjustment($order_id, $refund_amount);

            // 获取佣金调整记录
            $adjustment_records = Db::name('commission_adjustment')
                ->where('order_id', $order_id)
                ->select()
                ->toArray();

            $result = [
                'order_id' => $order_id,
                'original_commission' => $original_commission,
                'adjusted_commission' => $adjusted_commission,
                'adjustment_info' => $adjustment_info,
                'adjustment_records' => $adjustment_records
            ];

            return JsonServer::success('测试完成', $result);

        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新订单商品佣金
     */
    public function updateCommission()
    {
        $order_id = $this->request->post('order_id');

        if (!$order_id) {
            return JsonServer::error('请提供订单ID');
        }

        try {
            $result = CommissionLogic::updateOrderGoodsCommission($order_id);

            if ($result) {
                return JsonServer::success('更新成功');
            } else {
                return JsonServer::error('更新失败: ' . CommissionLogic::getError());
            }
        } catch (\Exception $e) {
            return JsonServer::error('更新失败: ' . $e->getMessage());
        }
    }
}
