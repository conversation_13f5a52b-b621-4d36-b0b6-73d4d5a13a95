{layout name="layout2" /}
<style>
    /*.layui-form {*/
    /*    margin: 5px;*/
    /*}*/
    /*.layui-form-label {*/
    /*    width: 120px;*/
    /*    text-align: left;*/
    /*    padding-left:30px;*/
    /*}*/
    .layui-input-block {
        width: 300px;
        line-height: 36px;
    }
    .layui-btn {
        margin-top: 5px;
    }
    select {
        width: 300px;
    }
    .layui-input {
        width: 300px;
    }
</style>
<form class="layui-form">
    <input type="hidden" name="user_id" id="user_id"  value="0">
    <div class="layui-form-item">
        <label class="layui-form-label">用户信息</label>
        <div class="layui-inline">
            <span id="user_selected"></span>
        </div>
        <div class="layui-inline">
            <button class="layui-btn layui-btn-sm layui-bg-blue" id="show-user">选择用户</button>
        </div>

        <!-- 集采众筹活动时间 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label class="layui-form-label"><font color="red">*</font>代理期限：</label>
            <div class="layui-input-block">
                <div class="layui-inline">
                    <input type="text" id="distribution_start_time" name="distribution_start_time" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
                </div>
                <div class="layui-inline">-</div>
                <div class="layui-inline">
                    <input type="text" id="distribution_end_time" name="distribution_end_time" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;margin-left:140px;">代理到期时,代理将冻结,将不能继续邀请下级及享受佣金分成.</div>
        </div>
    </div>


    <div class="layui-form-item">
        <div class="layui-input-block layui-hide">
            <button class="layui-btn" lay-submit lay-filter="openSubmit" id="openSubmit">立即提交</button>
        </div>
    </div>
</form>

<script>

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['element', 'form',"laydate",'layer'], function () {
        var $ = layui.$
            , form = layui.form
            , layer = layui.layer
            , element = layui.element;
        var laydate = layui.laydate;
        var layer = layui.layer;
        laydate.render({type:'date',elem:'#distribution_start_time',trigger:'click'});
        laydate.render({type:'date',elem:'#distribution_end_time',trigger:'click'});

        $('#show-user').click(function() {
            layer.open({
                type: 2
                ,title: "选择用户"
                ,content: "{:url('agent.agent/userLists')}"
                ,area: ["90%", "90%"]
                ,btn: ["确定", "取消"]
                ,yes: function(index, layero){
                    var iframeWindow = window["layui-layer-iframe" + index];
                    let user_selected = iframeWindow.user_selected();
                    $('#user_selected').html(user_selected.nickname + '(' + user_selected.sn + ')');
                    $('#user_id').val(user_selected.id);
                    layer.close(index);
                }
            });
            return false;
        });
    });
</script>
