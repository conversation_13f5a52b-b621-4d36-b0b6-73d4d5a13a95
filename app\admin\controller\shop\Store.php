<?php


namespace app\admin\controller\shop;


use app\admin\logic\shop\ApplyLogic;
use app\admin\logic\shop\CategoryLogic;
use app\admin\logic\shop\StoreLogic;
use app\admin\validate\shop\StoreLValidate;
use app\admin\validate\shop\StoreStatusValidate;
use app\common\basics\AdminBase;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use think\facade\Log;

/**
 * 商家管理
 * Class Store
 * @package app\admin\controller\shop
 */
class Store extends AdminBase
{
    /**
     * NOTE: 商家列表
     * @author: 张无忌
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
//            $lists = StoreLogic::migrateGoods($get);
//                        var_dump($lists);die;
            $lists = StoreLogic::lists($get);

            return JsonServer::success('获取成功', $lists);
        }

        return view('', [
            'category' => CategoryLogic::getCategory()
        ]);
    }

    /**
     * NOTE: 新增商家
     * @author: 张无忌
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            (new StoreLValidate())->goCheck('add');
            $post = $this->request->post();
            $lists = StoreLogic::add($post);
            if ($lists === false) {
                $error = StoreLogic::getError() ?: '新增失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('新增成功');
        }

        return view('', [
            'category' => CategoryLogic::getCategory(),
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }

    /**
     * NOTE: 编辑商家
     * @author: 张无忌
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            (new StoreLValidate())->goCheck('edit');
            $post = $this->request->post();
            if (!empty($post['password'])) {
                (new StoreLValidate())->goCheck('pwd');
            }

            $res = StoreLogic::edit($post);
            if ($res === false) {
                $error = StoreLogic::getError() ?: '编辑失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('编辑成功');
        }

        $id = $this->request->get('id');
        return view('', [
            'detail'   => StoreLogic::detail($id),
            'category' => CategoryLogic::getCategory(),
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }

    /**
     * NOTE: 设置商家
     * @author: 张无忌
     */
    public function set()
    {
        if ($this->request->isAjax()) {
            (new StoreLValidate())->goCheck('set');
            $post = $this->request->post();
            $res = StoreLogic::set($post);

            if ($res === false) {
                $error = StoreLogic::getError() ?: '设置失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('设置成功');
        }
        $get['page']=1;
        $get['limit']=100;
        $levels=ApplyLogic::levelList($get);
        $id = $this->request->get('id');
        $detail = StoreLogic::detail($id);

        // 获取检验人员昵称
        $staff_nickname = '';
        if (!empty($detail['f_user'])) {
            $staff = \app\common\model\user\User::where('id', $detail['f_user'])->value('nickname');
            $staff_nickname = $staff ?: '未知';
        }

        return view('', [
            'detail' => $detail,
            'levels' => $levels['lists'],
            'staff_nickname' => $staff_nickname
        ]);
    }

    /**
     * NOTE: 编辑账号
     * @author: 张无忌
     */
    public function account()
    {
        if ($this->request->isAjax()) {
            (new StoreLValidate())->goCheck('account');
            $post = $this->request->post();
            if (!empty($post['password'])) {
                (new StoreLValidate())->goCheck('pwd');
            }

            $res = StoreLogic::account($post);
            if ($res === false) {
                $error = StoreLogic::getError() ?: '更新失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('更新成功');
        }

        $id = $this->request->get('id');
        return view('', [
            'detail' => StoreLogic::getAccountInfo($id)
        ]);
    }

    /**
     * @notes 批量操作
     * @return \think\response\Json|void
     * <AUTHOR>
     * @date 2022/3/17 10:42
     */
    public function batchOperation()
    {
        if ($this->request->isAjax()) {
            (new StoreStatusValidate())->goCheck();
            $post = $this->request->post();
            $res = StoreLogic::batchOperation($post['ids'], $post['field'], $post['value']);
            if (false === $res) {
                $error = StoreLogic::getError() ?: '操作失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('操作成功');
        }
    }

    /**
     * @notes 搜索检验人员
     * @return \think\response\Json
     * <AUTHOR> @date
     */
    public function searchStaff()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = StoreLogic::searchStaff($get);
            return JsonServer::success('获取成功', $result);
        }
    }

    /**
     * @notes 分配检验人员
     * @return \think\response\Json
     * <AUTHOR> @date
     */
    public function assignStaff()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = StoreLogic::assignStaff($post);
            if ($result === false) {
                $error = StoreLogic::getError() ?: '分配失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('分配成功');
        }
    }

    /**
     * @notes 商家检验页面
     * @return \think\response\View
     * <AUTHOR> @date
     */
    public function inspection()
    {
        $shop_id = $this->request->get('shop_id');
        $inspection = StoreLogic::getInspection($shop_id);
        $shop_name = StoreLogic::getShopName($shop_id);
        return view('', [
            'inspection' => $inspection,
            'shop_id' => $shop_id,
            'shop_name' => $shop_name
        ]);
    }

    /**
     * 保存商家检验信息
     */
    public function saveInspection()
    {
        $data = $this->request->post();
        $result = StoreLogic::saveInspection($data);
        if ($result) {
            return JsonServer::success('保存成功');
        } else {
            return JsonServer::error(StoreLogic::getError());
        }
    }

    /**
     * 审核商家检验信息
     */
    public function auditInspection()
    {
        $data = $this->request->post();
        $result = StoreLogic::auditInspection($data);
        if ($result['code']) {
            return JsonServer::success($result['msg']);
        } else {
            return JsonServer::error($result['msg']);
        }
    }

    /**
     * 获取商家检验状态
     */
    public function getInspectionStatus()
    {
        $shop_id = $this->request->get('shop_id');

        // 添加调试日志
        \think\facade\Log::info('获取检验状态请求 - shop_id: ' . $shop_id);

        $inspection = StoreLogic::getInspection($shop_id);

        // 添加调试日志
        \think\facade\Log::info('检验数据查询结果: ' . json_encode($inspection, JSON_UNESCAPED_UNICODE));

        if ($inspection) {
            return JsonServer::success('获取成功', $inspection);
        } else {
            return JsonServer::success('获取成功', []);
        }
    }
}
