<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>保证金详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__ADMIN_PATH__/css/layui.css" media="all">
    <link rel="stylesheet" href="__ADMIN_PATH__/css/admin.css" media="all">
    <style>
        .detail-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .detail-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-left: 4px solid #5FB878;
            padding-left: 10px;
        }
        .detail-item {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
        }
        .detail-label {
            width: 120px;
            color: #666;
            font-weight: 500;
        }
        .detail-value {
            flex: 1;
            color: #333;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .status-0 { background-color: #999; }
        .status-1 { background-color: #5FB878; }
        .status-2 { background-color: #FFB800; }
        .status-3 { background-color: #FF5722; }
        .status-4 { background-color: #01AAED; }
        .status-5 { background-color: #F56C6C; }
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .amount-highlight {
            color: #FF5722;
            font-size: 18px;
            font-weight: bold;
        }
        .balance-highlight {
            color: #5FB878;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- 基本信息 -->
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">基本信息</div>
                <div class="detail-item">
                    <div class="detail-label">保证金ID：</div>
                    <div class="detail-value">{$deposit.id|default=''}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">用户信息：</div>
                    <div class="detail-value">
                        <div class="user-info">
                            <img src="{$deposit.user_avatar|default='__ADMIN_PATH__/images/default_avatar.png'}" class="user-avatar">
                            <div>
                                <div style="font-weight: bold; font-size: 16px;">{$deposit.user_nickname|default='未知用户'}</div>
                                <div style="color: #999; margin-top: 5px;">编号: {$deposit.user_sn|default=''}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">保证金金额：</div>
                    <div class="detail-value">
                        <span class="amount-highlight">¥{$deposit.amount|default=0}</span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">当前余额：</div>
                    <div class="detail-value">
                        <span class="balance-highlight">¥{$deposit.current_balance|default=0}</span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">状态：</div>
                    <div class="detail-value">
                        {switch name="$deposit.status|default=0"}
                            {case value="0"}
                                <span class="status-badge status-0">未支付</span>
                            {/case}
                            {case value="1"}
                                <span class="status-badge status-1">已支付</span>
                            {/case}
                            {case value="2"}
                                {if condition="$deposit.refund_request_time > 0"}
                                    <span class="status-badge status-2">退款中(公示期)</span>
                                {else/}
                                    <span class="status-badge status-2">公示期</span>
                                {/if}
                            {/case}
                            {case value="3"}
                                <span class="status-badge status-3">退款申请中</span>
                            {/case}
                            {case value="4"}
                                <span class="status-badge status-4">已退款</span>
                            {/case}
                            {case value="5"}
                                <span class="status-badge status-5">退款失败</span>
                            {/case}
                            {default /}
                                <span class="status-badge status-0">未知状态</span>
                        {/switch}
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间信息 -->
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">时间信息</div>
                <div class="detail-item">
                    <div class="detail-label">创建时间：</div>
                    <div class="detail-value">
                        {if condition="$deposit.create_time > 0"}
                            {$deposit.create_time|date='Y-m-d H:i:s'}
                        {else/}
                            -
                        {/if}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">支付时间：</div>
                    <div class="detail-value">
                        {if condition="$deposit.payment_date > 0"}
                            {$deposit.payment_date|date='Y-m-d H:i:s'}
                        {else/}
                            -
                        {/if}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">退款申请时间：</div>
                    <div class="detail-value">
                        {if condition="$deposit.refund_request_time > 0"}
                            {$deposit.refund_request_time|date='Y-m-d H:i:s'}
                        {else/}
                            -
                        {/if}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">退款时间：</div>
                    <div class="detail-value">
                        {if condition="$deposit.refund_time > 0"}
                            {$deposit.refund_time|date='Y-m-d H:i:s'}
                        {else/}
                            -
                        {/if}
                    </div>
                </div>
                {if condition="$deposit.refund_publicity_end_time > 0"}
                <div class="detail-item">
                    <div class="detail-label">公示期结束时间：</div>
                    <div class="detail-value">
                        {$deposit.refund_publicity_end_time|date='Y-m-d H:i:s'}
                    </div>
                </div>
                {/if}
            </div>
        </div>

        <!-- 支付信息 -->
        {if condition="$deposit.payment_method || $deposit.transaction_id"}
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">支付信息</div>
                {if condition="$deposit.payment_method"}
                <div class="detail-item">
                    <div class="detail-label">支付方式：</div>
                    <div class="detail-value">{$deposit.payment_method|default='-'}</div>
                </div>
                {/if}
                {if condition="$deposit.transaction_id"}
                <div class="detail-item">
                    <div class="detail-label">交易号：</div>
                    <div class="detail-value">{$deposit.transaction_id|default='-'}</div>
                </div>
                {/if}
            </div>
        </div>
        {/if}

        <!-- 备注信息 -->
        {if condition="$deposit.remark"}
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">备注信息</div>
                <div class="detail-item">
                    <div class="detail-label">备注：</div>
                    <div class="detail-value">{$deposit.remark|default='-'}</div>
                </div>
            </div>
        </div>
        {/if}
    </div>
</div>

<script src="__ADMIN_PATH__/layui.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    // 页面加载完成
    console.log('保证金详情页面加载完成');
});
</script>
</body>
</html>
