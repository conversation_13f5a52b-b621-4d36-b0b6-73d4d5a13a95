{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">
            <div class="layui-tab-item layui-show">
                <div class="layui-card">
                    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                        <div class="layui-form-item type">
                            <div class="layui-inline">
                                <label class="layui-form-label">商品名称:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="keyword" id="keyword" placeholder="请输入关键词" autocomplete="off" class="layui-input">
                                </div>
                            </div>


                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-like {$view_theme_color}" lay-submit lay-filter="like-table-search">查询</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table id="like-table-lists" lay-filter="like-table-lists"></table>
                        <script type="text/html" id="goods-info">
                            <img src="{{d.image}}" style="height:60px;width: 60px" class="image-show"> {{d.name}}
                        </script>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="select-submit" class="select-submit" id="select-submit" value="确认">
    </div>
</div>
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<script>
    var table;
    layui.use(["table", "element", "laydate"], function(){
        var $ = layui.$;
        var form = layui.form;
        var like = layui.like;
        var element = layui.element;
       table   = layui.table;

        //监听搜索
        form.on('submit(like-table-search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('like-table-lists', {
                where: field
            });
        });
    });
    like.tableLists('#like-table-lists', '{:url("common.goods/selectGoods")}', [
        {type: 'radio'}
        , {field: 'name', title: '商品名称', toolbar: '#goods-info'}
        , {field: 'price',  title: '价格'}
        , {field: 'stock', title: '库存'}
    ]);

    var callbackdata = function () {
        var data = table.checkStatus('like-table-lists').data
            ,index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        return data;
    }

</script>