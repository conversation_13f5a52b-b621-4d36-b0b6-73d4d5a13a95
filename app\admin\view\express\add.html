{layout name="layout2" /}
<script src="__PUBLIC__/static/common/js/area.js"></script>
<style>
    .layui-form-label {
        color: #6a6f6c;
    }
    html{
        background-color: #ffffff;
    }
    .reqRed::before {
        content: '*';
        color: red;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form-unit" id="layuiadmin-form-unit" style="padding: 20px 30px 0 0;background-color: #ffffff">
    <input type="hidden" value="0" name="id">
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">快递公司：</label>
        <div class="layui-input-inline" >
            <input type="text" name="name" lay-verify="required" lay-vertype="tips" placeholder="请输入快递公司" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item" style="margin-bottom: 0px">
        <label class="layui-form-label reqRed">快递图标：</label>
        <div class="layui-input-block" id="Couriericon">
            <div class="like-upload-image">
                <div class="upload-image-elem"><a class="add-upload-image" id="courier_icon"> + 添加图片</a></div>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <span style="color: #a3a3a3;font-size: 9px">快递图标，建议宽高尺寸200px*200px。</span>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">公司网址：</label>
        <div class="layui-input-inline" >
            <input type="text" name="website"  lay-vertype="tips" placeholder="请输入公司网址" autocomplete="off" class="layui-input">
            <label class=" layui-form-mid layui-word-aux" ></label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">快递编码：</label>
        <div class="layui-input-inline" >
            <input type="text" name="code"  lay-vertype="tips" placeholder="请输入快递编码" autocomplete="off" class="layui-input">
            <label class=" layui-form-mid layui-word-aux" >快递公司拼音简称。</label>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space: nowrap">快递100编码：</label>
        <div class="layui-input-inline" >
            <input type="text" name="code100"  lay-vertype="tips" placeholder="请输入快递100编码" autocomplete="off" class="layui-input">
            <label class=" layui-form-mid layui-word-aux" style="white-space: nowrap" >快递公司在快递100平台的编码，方便快递查询跟踪。</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space: nowrap;left: 10px;">快递鸟编码：</label>
        <div class="layui-input-inline" >
            <input type="text" name="codebird"  lay-vertype="tips" placeholder="请输入快递鸟" autocomplete="off" class="layui-input">
            <label class=" layui-form-mid layui-word-aux" style="white-space: nowrap" >快递公司在快递鸟平台的编码，方便快递查询跟踪。</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">排序：</label>
        <div class="layui-input-inline" >
            <input type="text" name="sort"  lay-vertype="tips" placeholder="请输入排序" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="express-submit" id="express-submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).extend({
        likeedit: 'likeedit/likeedit'
    }).use(['table', 'form', 'element', 'likeedit'], function() {
        var form = layui.form
            ,$ = layui.$
            , element = layui.element
            , likeedit = layui.likeedit;

        // 监听图片删除
        like.delUpload();
        // 快递图标上传
        $(document).on("click", "#courier_icon", function () {
            like.imageUpload({
                limit: 1,
                field: "poster",
                that: $(this),
                content: '{:url("file/lists")}?type=10'
            });
        })


    })
</script>