<?php


namespace app\admin\controller\shop;

use app\common\server\UrlServer;
use app\admin\logic\shop\ApplyLogic;
use app\admin\logic\shop\ShopDepositLogic;
use app\admin\logic\user\LevelLogic;
use app\admin\logic\user\UserLogic;
use app\admin\validate\shop\ShopApplyValidate;
use app\admin\validate\shop\ShopLevelValidate;
use app\admin\validate\user\LevelValidate;
use app\admin\validate\user\UserValidate;
use app\common\basics\AdminBase;
use app\common\model\shop\ShopDepositDetails;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use think\exception\ValidateException;
use think\facade\Db;

/**
 * 商家入驻
 * Class Apply
 * @package app\admin\controller\shop
 */
class Apply extends AdminBase
{
    /**
     * NOTE: 申请列表
     * @author: 张无忌
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = ApplyLogic::lists($get);
            return JsonServer::success('获取成功', $lists);
        }

        return view('', [
            'totalCount' => ApplyLogic::totalCount()
        ]);
    }
    public function add()
    {
        if($this->request->isAjax()) {
            try{
                $post = $this->request->post();
                validate(ShopLevelValidate::class)->scene('add')->check($post);
            }catch(ValidateException $e) {
                return JsonServer::error($e->getError());
            }
            $result = ApplyLogic::add($post);
            if($result === true) {
                return JsonServer::success('添加成功');
            }
            return JsonServer::error(LevelLogic::getError());
        }
        return view();
    }

    public function edit(){
        if($this->request->isAjax()){
            try{
                $post = $this->request->post();
                validate(ShopLevelValidate::class)->scene('edit')->check($post);
            }catch(ValidateException $e) {
                return JsonServer::error($e->getError());
            }
            $result = ApplyLogic::edit($post);
            if($result === true) {
                return JsonServer::success('编辑成功');
            }
            return JsonServer::error(LevelLogic::getError());
        }

        $id = $this->request->get('id', '', 'intval');
        $detail = ApplyLogic::getUserLevel($id);
        return view('', [
            'detail' => $detail
        ]);
    }

    public function del()
    {
        $id = $this->request->post('id', '',  'intval');
        $result = ApplyLogic::del($id);
        if($result === true) {
            return JsonServer::success('删除成功');
        }
        return JsonServer::error(LevelLogic::getError());
    }
    /**
     * NOTE: 统计
     * @author: 张无忌
     */
    public function totalCount()
    {
        if ($this->request->isAjax()) {
            return JsonServer::success('获取成功', ApplyLogic::totalCount());
        }

        return JsonServer::error('请求异常');
    }

    /**
     * NOTE: 详细
     * @author: 张无忌
     */
    public function detail()
    {
        (new ShopApplyValidate())->goCheck('id');
        $id = $this->request->get('id');
        return view('', [
            'detail' => ApplyLogic::detail($id)
        ]);
    }


    /**
     * NOTE: 详细
     * @author: 张无忌
     */
    public function depositdetail()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = ShopDepositLogic::getRelationData($get);
            return JsonServer::success('', $result);
        }
        $id = $this->request->get('id');
        return view('', [
            'detail' => ShopDepositLogic::detail($id)
        ]);
    }

    /**
     * NOTE: 审核
     * @author: 张无忌
     */
    public function audit()
    {
        if ($this->request->isAjax()) {
            (new ShopApplyValidate())->goCheck('audit');
            $post = $this->request->post();
            $res = ApplyLogic::audit($post);
            if ($res) {
                return JsonServer::success('操作成功');
            }

            $error = ApplyLogic::getError() ?: '操作失败';
            return JsonServer::error($error);
        }

        return view();
    }



    /*
     * 保证金列表
     * @author:DM
     */
    public function deposit(){

        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = ApplyLogic::depositList($get);
            return JsonServer::success('获取成功', $lists);
        }
        return view('', [
            'totalCount' => ApplyLogic::totalCount()
        ]);
    }

    /**
     * 保证金审核
     * @return \think\response\Json|\think\response\View
     */
    public function depositAudit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['id'])) {
                return JsonServer::error('参数错误');
            }

            // 验证状态
            if (!in_array($post['status'], ['1', '2'])) {
                return JsonServer::error('审核状态错误');
            }

            // 如果是拒绝，必须有理由
            if ($post['status'] == '2' && empty($post['remark'])) {
                return JsonServer::error('拒绝时必须填写理由');
            }

            // 调用逻辑层处理审核
            $result = ShopDepositLogic::audit($post);
            if ($result) {
                return JsonServer::success('审核成功');
            }

            $error = ShopDepositLogic::getError() ?: '审核失败';
            return JsonServer::error($error);
        }

        $id = $this->request->get('id');

        // 获取保证金信息
        $deposit = Db::name('shop_deposit')->where('id', $id)->find();
        if (!$deposit) {
            return JsonServer::error('保证金记录不存在');
        }

        // 获取合同文件路径
        $docs = $deposit['docs'] ?UrlServer::getFileUrl($deposit['docs']) : '';

        return view('deposit_audit', [
            'docs' => $docs
        ]);
    }


    /*
     * 商家等级
     * @author:DM
     */
    public function levels(){
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = ApplyLogic::levelList($get);
            return JsonServer::success('获取成功', $lists);
        }
        return view('', [
            'totalCount' => ApplyLogic::totalCount(),
            'entry_fee'  => ConfigServer::get('shop_entry', 'entry_fee', 0)
        ]);
    }

    /*
     * 添加等级信息
     * @author:DM
     */
    public function addLevel(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = ApplyLogic::addLevel($post);
            if ($res) {
                return JsonServer::success('操作成功');
            }
        }
        return view();
    }
    /*
     * 修改等级信息
     * @author:DM
     */
    public function editLevel(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = ApplyLogic::editLevel($post);
            if ($res) {
                return JsonServer::success('操作成功');
            }
        }
        return view();
    }

    /*
     * 删除等级信息
     * @author:DM
     */
    public function delLevel(){
        if ($this->request->isAjax()) {
            $id = $this->request->post('id');
            $res = ApplyLogic::delLevel($id);
            if ($res) {
                return JsonServer::success('删除成功');
            }
        }
        return JsonServer::error('删除失败');
    }


    public function editMoney(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['deposit_id']) || empty($post['shop_id']) ||
                empty($post['change_type']) || empty($post['amount']) ||
                empty($post['reason'])) {
                return JsonServer::error('请填写完整的表单信息');
            }

            // 调用逻辑层处理保证金调整
            $result = ShopDepositLogic::adjustDeposit($post);
            if($result === true) {
                return JsonServer::success('保证金调整成功');
            }
            return JsonServer::error(ShopDepositLogic::getError());
        }

        $id = $this->request->get('id/d');

        // 获取保证金信息
        $deposit = Db::name('shop_deposit')->where(['id' => $id])->find();
        if (!$deposit) {
            return JsonServer::error('保证金记录不存在');
        }

        // 获取商家信息
        $shop = Db::name('shop')->where(['id' => $deposit['shop_id']])->find();
        $shop_name = $shop ? $shop['name'] : '未知商家';

        // 计算当前余额
        $current_amount = ShopDepositLogic::calculateCurrentBalance($id);

        // 准备视图数据
        $deposit['shop_name'] = $shop_name;
        $deposit['current_amount'] = $current_amount;

        return view('edit_money', [
            'deposit' => $deposit
        ]);
    }



    /**
     * 退集采联盟商家保证金
     */
    public function refundAndCloseShop()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['deposit_id'])) {
                return JsonServer::error('参数错误');
            }

            // 添加管理员ID
            $post['admin_id'] = $this->adminId;

            // 调用逻辑层处理退费关店
            $result = ShopDepositLogic::refundAndCloseJcShop($post);
            if($result === true) {
                return JsonServer::success('退费关店操作成功');
            }
            return JsonServer::error(ShopDepositLogic::getError());
        }

        $id = $this->request->get('id/d');

        // 获取保证金信息
        $deposit = Db::name('shop_deposit')->where(['id' => $id])->find();
        if (!$deposit) {
            return JsonServer::error('保证金记录不存在');
        }

        // 获取商家信息
        $shop = Db::name('shop')->where(['id' => $deposit['shop_id']])->find();
        $shop_name = $shop ? $shop['name'] : '未知商家';

        // 计算当前余额
        $current_amount = ShopDepositLogic::calculateCurrentBalance($id);

        // 准备视图数据
        $deposit['shop_name'] = $shop_name;
        $deposit['current_amount'] = $current_amount;

        return view('refund_close_shop', [
            'deposit' => $deposit
        ]);
    }

    /**
     * 退款详情
     */
    public function refundDetail()
    {
        $id = $this->request->get('id/d');

        // 获取退款记录
        $refund = Db::name('common_refund')
            ->where(['source_id' => $id, 'refund_type' => \app\common\logic\CommonRefundLogic::REFUND_TYPE_SHOP_DEPOSIT])
            ->order('id', 'desc')
            ->find();

        if (!$refund) {
            return JsonServer::error('退款记录不存在');
        }

        // 获取商家信息
        $shop = Db::name('shop')->where(['id' => $refund['shop_id']])->find();
        $refund['shop_name'] = $shop ? $shop['name'] : '未知商家';

        return view('refund_detail', [
            'refund' => $refund
        ]);
    }

    /**
     * 重试退款
     */
    public function retryRefund()
    {
        if (!$this->request->isAjax()) {
            return JsonServer::error('非法请求');
        }

        $refund_id = $this->request->post('refund_id/d');
        if (empty($refund_id)) {
            return JsonServer::error('参数错误');
        }

        // 获取退款记录
        $refund = Db::name('common_refund')->where(['id' => $refund_id])->find();
        if (!$refund) {
            return JsonServer::error('退款记录不存在');
        }

        // 检查退款状态
        if ($refund['refund_status'] != \app\common\logic\CommonRefundLogic::REFUND_STATUS_FAILED) {
            return JsonServer::error('只有失败的退款才能重试');
        }

        // 准备重试参数
        $retry_params = [
            'refund_type' => $refund['refund_type'],
            'source_id' => $refund['source_id'],
            'shop_id' => $refund['shop_id'],
            'user_id' => $refund['user_id'],
            'refund_amount' => $refund['refund_amount'],
            'total_amount' => $refund['total_amount'],
            'payment_method' => $refund['payment_method'],
            'admin_id' => $this->admin_id,
            'remark' => '管理员重试退款',
            'order_source' => 'oa' // 默认来源
        ];

        // 调用通用退款逻辑
        $result = \app\common\logic\CommonRefundLogic::refund($retry_params);
        if ($result) {
            return JsonServer::success('重新退款操作成功');
        }

        return JsonServer::error(\app\common\logic\CommonRefundLogic::getError() ?: '重新退款失败');
    }

    /**
     * 处理退款申请（公示期结束后）
     */
    public function processRefund()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['deposit_id'])) {
                return JsonServer::error('参数错误');
            }

            // 获取保证金记录
            $deposit = Db::name('shop_deposit')->where('id', $post['deposit_id'])->find();
            if (!$deposit) {
                return JsonServer::error('保证金记录不存在');
            }

            // 检查退款状态
            if ($deposit['refund_status'] != 1) {
                return JsonServer::error('当前状态不允许处理退款');
            }

            // 检查公示期是否结束
            if (!empty($deposit['refund_publicity_end_time'])) {
                $current_time = time();
                $publicity_end_time = strtotime($deposit['refund_publicity_end_time']);

                if ($current_time < $publicity_end_time) {
                    return JsonServer::error('公示期尚未结束，无法处理退款');
                }
            }

            // 处理退款申请
            if ($post['status'] == '2') {
                // 同意退款
                $refund_params = [
                    'refund_type' => \app\common\logic\CommonRefundLogic::REFUND_TYPE_SHOP_DEPOSIT,
                    'source_id' => $deposit['id'],
                    'shop_id' => $deposit['shop_id'],
                    'user_id' => $deposit['user_id'],
                    'refund_amount' => ShopDepositLogic::calculateCurrentBalance($deposit['id']),
                    'total_amount' => $deposit['deposit_amount'],
                    'payment_method' => $deposit['payment_method'],
                    'transaction_id' => $deposit['transaction_id'] ?? '',
                    'admin_id' => session('admin.admin_id'),
                    'remark' => $post['remark'] ?? '管理员同意退款申请',
                    'order_source' => 'oa'
                ];

                $result = \app\common\logic\CommonRefundLogic::refund($refund_params);
                if ($result) {
                    return JsonServer::success('退款处理成功');
                }

                return JsonServer::error(\app\common\logic\CommonRefundLogic::getError() ?: '退款处理失败');
            } else if ($post['status'] == '3') {
                // 拒绝退款
                if (empty($post['remark'])) {
                    return JsonServer::error('拒绝退款时必须填写备注说明');
                }

                // 更新保证金记录状态为拒绝退款
                $updateData = [
                    'refund_status' => 3, // 拒绝退款
                    'refund_remark' => $post['remark'],
                    'refund_admin_id' => session('admin.admin_id'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $result = Db::name('shop_deposit')
                    ->where('id', $deposit['id'])
                    ->update($updateData);

                if ($result) {
                    return JsonServer::success('已拒绝退款申请');
                }

                return JsonServer::error('操作失败');
            } else {
                return JsonServer::error('处理状态错误');
            }
        }

        $id = $this->request->get('id/d');

        // 获取保证金信息
        $deposit = Db::name('shop_deposit')->where(['id' => $id])->find();
        if (!$deposit) {
            return JsonServer::error('保证金记录不存在');
        }

        // 获取商家信息
        $shop = Db::name('shop')->where(['id' => $deposit['shop_id']])->find();
        $shop_name = $shop ? $shop['name'] : '未知商家';

        // 计算当前余额
        $current_amount = ShopDepositLogic::calculateCurrentBalance($id);

        // 准备视图数据
        $deposit['shop_name'] = $shop_name;
        $deposit['current_amount'] = $current_amount;

        return view('process_refund', [
            'deposit' => $deposit
        ]);
    }
}