<?php

namespace app\admin\controller\setting;

use app\common\basics\AdminBase;
use app\admin\logic\setting\UserActivityLogic;
use app\admin\validate\setting\UserActivityValidate;
use app\common\server\JsonServer;

/**
 * 用户活跃度设置控制器
 * Class UserActivity
 * @package app\admin\controller\setting
 */
class UserActivity extends AdminBase
{
    /**
     * 配置页面
     * @return string|\think\response\Json
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $config = UserActivityLogic::getConfig();
            $statistics = UserActivityLogic::getStatistics();
            $descriptions = UserActivityLogic::getConfigDescriptions();
            
            return JsonServer::success('获取成功', [
                'config' => $config,
                'statistics' => $statistics,
                'descriptions' => $descriptions,
            ]);
        }
        
        return view();
    }

    /**
     * 保存配置
     * @return \think\response\Json
     */
    public function save()
    {
        try {
            // 获取 POST 数据
            $post = $this->request->post();

            // 确保数据格式正确
            if (is_string($post)) {
                // 如果是字符串，尝试解析
                parse_str($post, $post);
            }

            // 验证数据
            $validate = new UserActivityValidate();
            if (!$validate->scene('save')->check($post)) {
                return JsonServer::error($validate->getError());
            }

            // 自定义验证等级递增
            $level_check = $validate->checkLevelProgression($post);
            if ($level_check !== true) {
                return JsonServer::error($level_check);
            }

            // 保存配置
            if (UserActivityLogic::saveConfig($post)) {
                return JsonServer::success('保存成功');
            } else {
                return JsonServer::error(UserActivityLogic::getError() ?: '保存失败');
            }
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 重新计算所有用户等级
     * @return \think\response\Json
     */
    public function recalculate()
    {
        try {
            if (UserActivityLogic::recalculateAllLevels()) {
                return JsonServer::success('重新计算完成');
            } else {
                return JsonServer::error(UserActivityLogic::getError() ?: '计算失败');
            }
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 活跃度日志列表
     * @return string|\think\response\Json
     */
    public function logs()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $data = UserActivityLogic::getActivityLogs($get);
            return JsonServer::success('获取成功', $data);
        }
        
        return view();
    }

    /**
     * 获取配置项说明
     * @return \think\response\Json
     */
    public function getDescriptions()
    {
        $descriptions = UserActivityLogic::getConfigDescriptions();
        return JsonServer::success('获取成功', $descriptions);
    }

    /**
     * 获取统计数据
     * @return \think\response\Json
     */
    public function getStatistics()
    {
        $statistics = UserActivityLogic::getStatistics();
        return JsonServer::success('获取成功', $statistics);
    }
}
