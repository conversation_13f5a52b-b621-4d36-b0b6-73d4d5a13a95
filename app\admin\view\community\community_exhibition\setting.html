{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--操作提示-->
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*设置发现相关的配置信息</p>
                        <p>*设置发现登记表单必填和选填项</p>
                    </div>
                </div>
            </div>

            <!--表单区域-->
            <div class="layui-form" style="margin-top: 15px;">
                <div class="layui-field-box">
                    <div class="layui-form-item">
                        <lable class="layui-form-label">公司名称：</lable>
                        <div class="layui-input-block" style="width:300px;">
                            <input type="radio" name="company" value="1" title="必填" {if $config.company == 1} checked {/if}>
                            <input type="radio" name="company" value="0" title="选填" {if !$config.company} checked {/if}>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <lable class="layui-form-label">姓名：</lable>
                        <div class="layui-input-block" style="width:300px;">
                            <input type="radio" name="name" value="1" title="必填" {if $config.name == 1} checked {/if}>
                            <input type="radio" name="name" value="0" title="选填" {if !$config.name} checked {/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <lable class="layui-form-label">电话：</lable>
                        <div class="layui-input-block" style="width:300px;">
                            <input type="radio" name="phone" value="1" title="必填" {if $config.phone == 1} checked {/if}>
                            <input type="radio" name="phone" value="0" title="选填" {if !$config.phone} checked {/if}>
                        </div>
                    </div>
                    <div class="layui-form-item backgroup">
                        <label class="layui-form-label" style="white-space:nowrap;">微信二维码：</label>
                        <div class="layui-input-block">
                            <div class="like-upload-image">
                                <div class="upload-image-elem"><a class="add-upload-image2"> + 添加图片</a></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <lable class="layui-form-label"></lable>
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="set">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).use(['form','element'], function(){
        var $ = layui.$,
            form = layui.form,
            element = layui.element;
        like.delUpload();
        $(document).on("click", ".add-upload-image2", function () {
            like.imageUpload({
                limit: 1,
                field: "wechat_image",
                that: $(this)
            });
        })
        form.on('submit(set)', function(data) {
            like.ajax({
                url:'{:url("community.CommunityExhibition/setting")}',
                data: data.field,
                type:"post",
                success:function(res)
                {
                    if(res.code == 1)
                    {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        });
                    }
                }
            });
        });

    });
</script>