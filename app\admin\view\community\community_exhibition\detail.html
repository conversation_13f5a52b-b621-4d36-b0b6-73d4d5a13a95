{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }
    .width-160 {
        width: 200px;
    }
    .image {
        height: 60px;
        width: 60px;
        margin-right: 5px;
    }
</style>

<div class="layui-card-body">
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-order" id="layuiadmin-form-order">
        <input type="hidden" id="article_id" name="id" value="{$detail.id}">

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>用户信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">展会负责人:</label>
            <div class="width-160">{$detail.user_name}</div>
        </div>
        <div className="layui-form-item">
            <fieldset className="layui-elem-field layui-field-title">
                <legend>内容</legend>


        <div class="layui-form-item div-flex">


            <label class="layui-form-label ">发布时间:</label>
            <div class="width-160">{$detail.create_time}</div>
        </div>


        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">发布内容:</label>
            <div class="width-160">{$detail.content}</div>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">发布图片:</label>
            <div>
                {volist name="detail.images" id="vo"}
                <img src="{$vo.image}" class="image-show image">
                {/volist}
            </div>
        </div>
        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">发布视频:</label>
            <div>
                {volist name="detail.videos" id="vo"}
                &nbsp;<video src={$vo.videos} height="100" data-type="2" class="preview-all"></video>

                {/volist}
            </div>
        </div>
        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">审核时间:</label>
            <div class="width-160">{$detail.audit_time}</div>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">审核说明:</label>
            <div class="width-160">{$detail.audit_remark}</div>
        </div> </fieldset>

<!--        <div class="layui-form-item">-->
<!--            <fieldset class="layui-elem-field layui-field-title">-->
<!--                <legend>信息数据</legend>-->
<!--            </fieldset>-->
<!--        </div>-->


<!--        <div class="layui-form-item">-->
<!--            <div class="layui-tab layui-tab-card" lay-filter="tab-all">-->
<!--                <ul class="layui-tab-title">-->
<!--                    <li data-type='comment' class="layui-this">评论</li>-->
<!--                    <li data-type='like' >点赞</li>-->
<!--                </ul>-->
<!--                <div class="layui-tab-item layui-show">-->
<!--                        <div class="layui-card-body">-->
<!--                            <table id="like-table-lists" lay-filter="like-table-lists"></table>-->
<!--                            <script type="text/html" id="table-userInfo">-->
<!--                                <div class="layui-inline" style="text-align:left;">-->
<!--                                    <p>用户编号：{{d.sn}}</p>-->
<!--                                    <p>用户昵称：{{d.nickname}}</p>-->
<!--                                </div>-->
<!--                            </script>-->
<!--                        </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

    </div>
</div>

<script type="text/javascript">
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form', 'table', 'element'], function () {
        var $ = layui.$;
        var table = layui.table;
        var element = layui.element;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src, 400);
        });

        //获取列表
        getList('comment');
        //切换列表
        element.on('tab(tab-all)', function (data) {
            var type = $(this).attr('data-type');
            getList(type);
        });
        $(document).on('click', '.preview-all', function (data) {
            // var obj = data.target.dataset;
            let clickObject = data.target; //点击的对象
            let url = clickObject.src; //图片、视频 地址
            previewVideo(url);
        });
        //视频预览，传url,width,height
        function previewVideo(url, width, height) {
            width = width ? width : '65%';
            height = height ? height : '65%';
            let content = '<video width="100%" height="90%"  controls="controls" autobuffer="autobuffer"  autoplay="autoplay" loop="loop">' +
                '<source src="' + url + '" type="video/mp4"></source></video>';
            layer.open({
                type: 1,
                maxmin: true, //打开放大缩小按钮
                title: '视频播放',
                area: [width, height],
                content: content,
            });
        }
        function getList(type) {
            var cols = [
                {field: 'user', title: '用户信息', align: 'center', templet: "#table-userInfo"}
                , {field: 'create_time', title: '点赞时间', align: 'center'}
            ];
            if (type === 'comment') {
                cols = [
                    {field: 'user', title: '评论用户', align: 'center', templet: "#table-userInfo"}
                    , {field: 'comment', title: '评论内容', align: 'center'}
                    , {field: 'create_time', title: '评价时间', align: 'center'}
                ];
            }
            var id = $('#article_id').val();
            like.tableLists("#like-table-lists", '{:url("community.CommunityArticle/detail")}?type='+ type + '&id=' + id, cols);
        }


    });
</script>