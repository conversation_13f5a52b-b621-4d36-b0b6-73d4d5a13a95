<?php



namespace app\admin\controller\system;

use app\admin\logic\system\CrontabLogic;
use app\admin\validate\system\CrontabValidate;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use think\exception\ValidateException;

/**
 * 系统缓存
 * Class Cache
 * @package app\admin\controller\system
 */
class Cache extends AdminBase
{
    public function cache()
    {
        if ($this->request->isAjax()) {
            \think\facade\Cache::clear();
            del_target_dir(app()->getRootPath().'runtime/file/export', true);
            return JsonServer::success('清除成功');
        }
        return view();
    }

    public function index(){

        if ($this->request->isAjax()) {
            \think\facade\Cache::clear();
            del_target_dir(app()->getRootPath().'runtime/file/export', true);
            return JsonServer::success('清除成功');
        }
        return view();

    }

    public function Searchadd()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['status'] = isset($post['status']) && $post['status'] == 'on' ? 1 : 2;
            $result = CrontabLogic::Searchadd($post);
            return JsonServer::success('添加成功');

        }
        return view('add');
    }
}