<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------

namespace app\admin\controller\live;


use app\admin\logic\live\LiveRoomLogic;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use app\admin\logic\live\LiveGoodsLogic;
use app\admin\validate\live\LiveGoodsValidate;


/**
 * 直播商品
 * Class LiveGoods
 * @package app\admin\controller\live
 */
class LiveGoods extends AdminBase
{

    /**
     * @notes 直播间列表
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2023/2/16 10:38
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = LiveGoodsLogic::lists($get);
            return JsonServer::success('', $lists);
        }
        return view('', [
            'shop' => LiveRoomLogic::shopLists()
        ]);
    }


    /**
     * @notes 添加直播商品
     * @return \think\response\Json|\think\response\View
     * @throws \GuzzleHttp\Exception\GuzzleException
     * <AUTHOR>
     * @date 2023/2/16 10:38
     */
    public function audit()
    {
        if ($this->request->isAjax()) {
            $params = (new LiveGoodsValidate())->goCheck('audit');
            $result = LiveGoodsLogic::audit($params);
            if ($result !== true) {
                return JsonServer::error(LiveGoodsLogic::getError());
            }
            return JsonServer::success('操作成功');
        }
        $id = $this->request->get('id');
        return view('', [
            'detail' => LiveGoodsLogic::detail($id),
        ]);
    }



    /**
     * @notes 直播商品详情
     * @return \think\response\View
     * <AUTHOR>
     * @date 2023/2/16 16:40
     */
    public function detail()
    {
        $params = (new LiveGoodsValidate())->goCheck('detail');
        return view('', [
            'detail' => LiveGoodsLogic::detail($params),
        ]);
    }


    /**
     * @notes 删除直播商品
     * @return \think\response\Json|void
     * <AUTHOR>
     * @date 2023/2/17 10:20
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            $params = (new LiveGoodsValidate())->goCheck('del');
            $result = LiveGoodsLogic::del($params);
            if ($result !== true) {
                return JsonServer::error(LiveGoodsLogic::getError());
            }
            return JsonServer::success('操作成功');
        }
    }

}