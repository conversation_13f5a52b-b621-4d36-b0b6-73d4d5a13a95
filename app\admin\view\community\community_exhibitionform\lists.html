{layout name="layout1" /}
<style>

</style>
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台管理展会的登记信息，平台可对信息进行查看，删除等操作</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="keyword" class="layui-form-label">提交人：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="keyword" name="keyword" autocomplete="off" class="layui-input" placeholder="请输入昵称/编号/手机号">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="content" class="layui-form-label">手机搜索：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="content" name="content" autocomplete="off" class="layui-input" placeholder="请输入展会内容">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="status" class="layui-form-label">登记类型：</label>
                    <div class="layui-input-inline" >
                        <select name="type_id" id="type_id" lay-search="" >
                            <option value="">全部</option>
                            <option value="0">展位咨询</option>
                            <option value="1">观展登记</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="status" class="layui-form-label">会展选择：</label>
                    <div class="layui-input-inline" style="width:300px;">
                        <select name="exhibition_id" id="exhibition_id" lay-search="" >
                            <option value="">全部</option>
                            {foreach $exhibition as $item => $val}
                            <option value="{$item}">{$val}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">提交时间:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input time" id="start_time" name="start_time"  autocomplete="off">
                    </div>
                    <div class="layui-input-inline" style="margin-right: 5px;width: 10px;">
                        <label class="layui-form-mid">-</label>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input time" id="end_time" name="end_time"  autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="data-export">导出</a>

                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
<!--            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm layEvent" lay-event="add">新增信息</button>-->

            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="table-operation">
<!--                <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>-->
                <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="detail">详情</a>
                <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
            </script>
            <script type="text/html" id="table-image">
                {{#  layui.each(d.images, function(index, item){ }}
                <img src="{{item.image}}" style="height:60px;width: 60px;margin-right: 5px;" class="image-show">
                {{#  }); }}
            </script>
            <script type="text/html" id="table-video">
                {{#  layui.each(d.videos, function(index, item){ }}
                <video src={{item.videos}} height="70" data-type="2" class="preview-all"></video>
                {{#  }); }}
            </script>
            <script type="text/html" id="table-userInfo">
                <img src="{{d.avatar}}" alt="图标" style="width:60px;height:60px;margin-right:5px;">
                <div class="layui-inline" style="text-align:left;">
                    <p>编号：{{d.sn}}</p>
                    <p>昵称：{{d.nickname}}</p>
                </div>
            </script>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form", "laydate"], function(){
        var table   = layui.table;
        var form   = layui.form;
        var laydate   = layui.laydate;

        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
        });

        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
        });

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"user",  align:"center",width: 240, title:"登记人", templet: "#table-userInfo"},
            {field:"title", width: 300,　align:"center", title:"会展标题"},
            {field:"phone", width: 200,title:"联系方式"}
            ,{field:"company", width: 200,　align:"center", title:"企业名称"}
            ,{field:"type_id",width: 200, 　align:"center", title:"提交类型"}
            ,{field:"sex", 　width: 100, align:"center", title:"性别"}
            // ,{field:"status_desc", 　width: 120, align:"center", title:"状态"}
            ,{field:"create_time", width: 180, align:"center", title:"提交时间"}
            ,{title:"操作", align:"left", fixed:"right",width: 220, toolbar:"#table-operation"}
        ]);

        var active = {
            audit: function(obj) {
                layer.open({
                    type: 2
                    ,title: "信息审核"
                    ,content: "{:url('community.CommunityExhibition/audit')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('community.CommunityExhibition/audit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            add: function() {
                layer.open({
                    type: 2
                    ,title: "新增展会信息"
                    ,content: "{:url('community.CommunityExhibition/add')}"
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            like.ajax({
                                url: "{:url('community.CommunityExhibition/add')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            edit: function(obj) {
                layer.open({
                    type: 2
                    ,title: "编辑展会信息"
                    ,content: "{:url('community.CommunityExhibition/edit')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('community.CommunityExhibition/edit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            detail: function(obj) {
                layer.open({
                    type: 2
                    ,title: "详情"
                    ,content: "{:url('community.CommunityExhibitionform/detail')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,yes: function(index, layero){
                    }
                });
            },
            del: function(obj) {
                var name =  "<div style='width:500px;overflow:hidden;text-overflow: ellipsis;white-space:nowrap;'>" +
                    "<span>确定删除展会信息,删除后将<span style='color: red;'>清空</span>登记信息：</span>" +
                    "<span style='color: red;'>"+obj.data.title +"</span></div>";

                layer.confirm(name, function(index) {
                    like.ajax({
                        url: "{:url('community.CommunityExhibitionform/del')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            }
        };
        like.eventClick(active);

//监听点击方法
        $(document).on('click', '.preview-all', function (data) {
            // var obj = data.target.dataset;
            let clickObject = data.target; //点击的对象
            let url = clickObject.src; //图片、视频 地址
            previewVideo(url);
        });
        //视频预览，传url,width,height
        function previewVideo(url, width, height) {
            width = width ? width : '65%';
            height = height ? height : '65%';
            let content = '<video width="100%" height="90%"  controls="controls" autobuffer="autobuffer"  autoplay="autoplay" loop="loop">' +
                '<source src="' + url + '" type="video/mp4"></source></video>';
            layer.open({
                type: 1,
                maxmin: true, //打开放大缩小按钮
                title: '视频播放',
                area: [width, height],
                content: content,
            });
        }

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });

        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });
// 导出
        form.on('submit(data-export)', function (data) {
            var field = data.field;
            like.ajax({
                  url: "{:url('community.CommunityExhibitionform/export')}"
                , data: field
                , type: 'get'
                , success: function (res) {
                    if (res.code == 1) {
                        window.location.href = res.data.url;
                    }
                }
            });
        });
        form.on("submit(clear-search)", function(){
            $("#keyword").val("");
            $("#content").val("");
            $("#status").val("");
            $("#start_time").val("");
            $("#end_time").val("");
            form.render("select");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });
    })
</script>