{layout name="layout1" /}
<!-- 样式 -->
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台维护商品品牌。</p>
                        <p>*商家发布商品时可以选择对应的品牌，用户可以根据商品品牌搜索商品。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <!--搜索条件-->
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">品牌名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" id="keyword" placeholder="请输入品牌名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm layuiadmin-btn-goods_brand {$view_theme_color}" lay-submit lay-filter="goods_brand-search">
                            <i class="layui-icon  layuiadmin-button-btn"></i>查询
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm  layui-btn-primary layuiadmin-btn-goods_brand  " lay-submit lay-filter="goods_brand-clear-search">清空查询</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <!--添加按钮-->
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm  {$view_theme_color} layEvent"  lay-event="add">新增品牌</button>
            </div>

            <!--表格-->
            <table id="like-table-lists" lay-filter="like-table-lists"></table>

            <script type="text/html" id="statusTpl">
                <input type="checkbox"  lay-filter="switch-status" data-id={{d.id}} lay-skin="switch"
                       lay-text="显示|隐藏" {{#  if(d.is_show){ }} checked  {{#  } }} />
            </script>

            <script type="text/html" id="goodsBrand-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
            </script>

            <script type="text/html" id="image">
                <img src="{{d.image}}" style="height:80px;width: 80px" class="image-show">
            </script>
        </div>

    </div>
</div>

<script>

    layui.use(['table'], function(){
        var form = layui.form
            ,table = layui.table;

        //监听搜索
        form.on('submit(goods_brand-search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('like-table-lists', {
                where: field,
                page: {curr: 1},
            });
        });

        //清空查询
        form.on('submit(goods_brand-clear-search)', function () {
            $('#keyword').val('');
            //刷新列表
            table.reload('like-table-lists', {
                where: [],
                page: {curr: 1},
            });
        });

        //切换状态
        form.on('switch(switch-status)',function (obj) {
            var id = obj.elem.attributes['data-id'].nodeValue;
            var is_show = 0;
            if(this.checked) {
                is_show = 1;
            }
            like.ajax({
                url:'{:url("goods.brand/switchStatus")}',
                data:{id:id,is_show:is_show},
                type:'post',
                success:function (res) {
                    if(res.code == 1) {
                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                    }
                }
            });
        });

        //事件
        var active = {
            add: function(){
                layer.open({
                    type: 2
                    ,title: '新增商品品牌'
                    ,content: '{:url("goods.brand/add")}'
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero) {
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'add-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data) {
                            var field = data.field;
                            like.ajax({
                                url:'{:url("goods.brand/add")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                   if(res.code == 1) {
                                       layui.layer.msg(res.msg, {
                                           offset: '15px'
                                           , icon: 1
                                           , time: 1000
                                       });
                                       layer.close(index);
                                       table.reload('like-table-lists');
                                   }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            },
            del: function (obj) {
                var unitName = "<span style='color: red;'>"+obj.data.name+"</span>";
                layer.confirm('确定删除商品品牌: '+unitName, function(index) {
                    like.ajax({
                        url:'{:url("goods.brand/del")}',
                        data:{'id':obj.data.id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1) {
                                obj.del();
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                layer.close(index);
                                table.reload('like-table-lists');
                            }
                        }
                    });
                });
            },
            edit: function (obj) {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑商品品牌'
                    ,content: '{:url("goods.brand/edit")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'edit-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("goods.brand/edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('like-table-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
        };
        like.eventClick(active);


        //列表
        like.tableLists('#like-table-lists', '{:url("goods.brand/lists")}', [
            {field: 'id', width: 60, title: 'ID', sort: true}
            ,{field: 'name', title: '品牌名称', align:"center"}
            ,{field: 'image', title: '图片', align:"center", templet: "#image"}
            ,{field: 'initial', title: '品牌首字母', align:"center"}
            ,{field: 'status', title: '显示状态', align:"center", templet:'#statusTpl'}
            ,{field: 'sort', title: '排序', align:"center"}
            ,{title: '操作', align: 'center', fixed: 'right', toolbar: '#goodsBrand-operation'}
        ]);

    });
</script>