<?php

namespace app\admin\validate\setting;

use app\common\basics\Validate;
use think\helper\Str;

class StorageValidate extends Validate
{
    protected $rule = [
        'domain' => 'require|checkDomain',
    ];
    
    protected $message = [
        'domain.require' => '空间域名必须填写',
    ];
    
    function sceneEdit()
    {
        $this->only([ 'domain' ]);
    }
    
    function checkDomain($domain, $rule, $data)
    {
        if (Str::contains($domain, 'http://') || Str::contains($domain, 'https://')) {
            return true;
        }
        
        return '空间域名请补全http://或https://';
    }
}