<?php

namespace app\admin\logic\shop;

use app\common\model\shop\Shop;
use app\shopapi\logic\ShopLogic;
use think\facade\Db;

/**
 * 商家注销逻辑
 * Class DeactivateLogic
 * @package app\admin\logic\shop
 */
class DeactivateLogic
{
    /**
     * @notes 错误信息
     * @var string
     */
    private static $error = '';

    /**
     * @notes 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }

    /**
     * @notes 注销申请列表
     * @param array $get
     * @return array
     */
    public static function lists(array $get)
    {
        $where = [];
        
        // 搜索条件
        if (!empty($get['shop_name'])) {
            $shopIds = Shop::where('name', 'like', '%' . $get['shop_name'] . '%')->column('id');
            if (!empty($shopIds)) {
                $where[] = ['shop_id', 'in', $shopIds];
            } else {
                $where[] = ['shop_id', '=', 0]; // 确保没有结果
            }
        }
        
        if (isset($get['status']) && $get['status'] !== '') {
            $where[] = ['status', '=', $get['status']];
        }
        
        // 时间筛选
        if (!empty($get['start_time']) && !empty($get['end_time'])) {
            $where[] = ['create_time', 'between', [strtotime($get['start_time']), strtotime($get['end_time']) + 86399]];
        }

        $count = Db::name('shop_deactivate_apply')->where($where)->count();
        $lists = Db::name('shop_deactivate_apply')
            ->alias('a')
            ->join('shop s', 'a.shop_id = s.id')
            ->join('shop_admin sa', 'a.admin_id = sa.id')
            ->where($where)
            ->field('a.*, s.name as shop_name, sa.name as admin_name')
            ->order('a.id desc')
            ->page($get['page'] ?? 1, $get['limit'] ?? 20)
            ->select()
            ->toArray();

        // 格式化数据
        foreach ($lists as &$item) {
            $item['status_text'] = self::getStatusText($item['status']);
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['audit_time'] = $item['audit_time'] ? date('Y-m-d H:i:s', $item['audit_time']) : '';
            
            // 解析检查结果
            if (!empty($item['check_result'])) {
                $checkResult = json_decode($item['check_result'], true);
                $item['can_deactivate'] = $checkResult['can_deactivate'] ?? false;
                $item['check_messages'] = $checkResult['messages'] ?? [];
            } else {
                $item['can_deactivate'] = false;
                $item['check_messages'] = [];
            }
        }

        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * @notes 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText(int $status)
    {
        $statusArr = [
            0 => '待审核',
            1 => '已通过',
            2 => '已拒绝'
        ];
        return $statusArr[$status] ?? '未知状态';
    }

    /**
     * @notes 注销申请详情
     * @param int $id
     * @return array
     */
    public static function detail(int $id)
    {
        $detail = Db::name('shop_deactivate_apply')
            ->alias('a')
            ->join('shop s', 'a.shop_id = s.id')
            ->join('shop_admin sa', 'a.admin_id = sa.id')
            ->where('a.id', $id)
            ->field('a.*, s.name as shop_name, sa.name as admin_name, s.wallet, s.mobile')
            ->find();

        if (!$detail) {
            return [];
        }

        $detail['status_text'] = self::getStatusText($detail['status']);
        $detail['create_time'] = date('Y-m-d H:i:s', $detail['create_time']);
        $detail['audit_time'] = $detail['audit_time'] ? date('Y-m-d H:i:s', $detail['audit_time']) : '';

        // 解析检查结果
        if (!empty($detail['check_result'])) {
            $checkResult = json_decode($detail['check_result'], true);
            $detail['can_deactivate'] = $checkResult['can_deactivate'] ?? false;
            $detail['conditions'] = $checkResult['conditions'] ?? [];
            $detail['check_messages'] = $checkResult['messages'] ?? [];
        } else {
            $detail['can_deactivate'] = false;
            $detail['conditions'] = [];
            $detail['check_messages'] = [];
        }

        // 获取保证金信息
        $deposit = Db::name('shop_deposit')
            ->where('shop_id', $detail['shop_id'])
            ->where('pay_status', 1) // 已支付状态
            ->find();

        if ($deposit) {
            $detail['has_deposit'] = true;
            $detail['deposit_amount'] = $deposit['deposit_amount'];
            $detail['payment_date'] = $deposit['payment_date'];
            
            // 计算支付时间距离现在的天数
            $paymentDate = strtotime($deposit['payment_date']);
            $now = time();
            $daysDiff = floor(($now - $paymentDate) / (60 * 60 * 24));
            
            $detail['deposit_days'] = $daysDiff;
            $detail['deposit_refund_type'] = $daysDiff <= 365 ? 'wechat' : 'transfer';
        } else {
            $detail['has_deposit'] = false;
        }

        return $detail;
    }

    /**
     * @notes 审核注销申请
     * @param array $post
     * @return bool
     */
    public static function audit(array $post)
    {
        Db::startTrans();
        try {
            $id = $post['id'];
            $status = $post['audit_status'];
            $remark = $post['audit_remark'] ?? '';

            // 获取申请信息
            $apply = Db::name('shop_deactivate_apply')->where('id', $id)->find();
            if (!$apply) {
                throw new \Exception('申请记录不存在');
            }

            if ($apply['status'] != 0) {
                throw new \Exception('该申请已审核，请勿重复操作');
            }

            // 更新申请状态
            Db::name('shop_deactivate_apply')->where('id', $id)->update([
                'status' => $status,
                'audit_time' => time(),
                'audit_remark' => $remark
            ]);

            // 如果审核通过，执行注销流程
            if ($status == 1) {
                // 这里不直接执行注销流程，而是在审核通过后由管理员手动执行
                // 因为注销流程可能涉及到资金退款等敏感操作，需要管理员确认
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 执行注销
     * @param array $post
     * @return array|bool
     */
    public static function execute(array $post)
    {
        Db::startTrans();
        try {
            $id = $post['id'];

            // 获取申请信息
            $apply = Db::name('shop_deactivate_apply')->where('id', $id)->find();
            if (!$apply) {
                throw new \Exception('申请记录不存在');
            }

            if ($apply['status'] != 1) {
                throw new \Exception('只有审核通过的申请才能执行注销');
            }

            // 执行注销流程
            $shopLogic = new ShopLogic();
            $result = $shopLogic->executeDeactivate($apply['shop_id']);

            if (!$result['success']) {
                throw new \Exception('执行注销失败：' . implode(', ', $result['messages']));
            }

            // 更新申请记录
            Db::name('shop_deactivate_apply')->where('id', $id)->update([
                'execute_time' => time(),
                'execute_result' => json_encode($result, JSON_UNESCAPED_UNICODE)
            ]);

            Db::commit();
            return $result;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }
}
