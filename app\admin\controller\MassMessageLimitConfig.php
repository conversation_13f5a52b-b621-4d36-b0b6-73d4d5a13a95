<?php
namespace app\admin\controller;

use app\admin\logic\MassMessageLimitLogic;

use app\common\basics\AdminBase;
use app\common\utils\AjaxUtils;
use app\common\server\JsonServer;

/**
 * 群发信息限制配置控制器
 * Class MassMessageLimitConfig
 * @package app\admin\controller
 */
class MassMessageLimitConfig extends AdminBase
{
    /**
     * 群发信息限制配置列表
     * @return string
     * @throws \Exception
     */
    public function index()
    {
        return view('', [
            'list' => MassMessageLimitLogic::getList()
        ]);
    }

    /**
     * 添加群发信息限制配置
     * @return string|\think\response\Json
     * @throws \Exception
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = MassMessageLimitLogic::add($post);
            return AjaxUtils::success($result);
        }
        return view('');
    }

    /**
     * 编辑群发信息限制配置
     * @return string|\think\response\Json
     * @throws \Exception
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = MassMessageLimitLogic::edit($post);
            return AjaxUtils::success($result);
        }
        $id = $this->request->get('id');
        return view('', [
            'info' => MassMessageLimitLogic::getInfo($id)
        ]);
    }

    /**
     * 删除群发信息限制配置
     * @return \think\response\Json
     * @throws \Exception
     */
    public function delete()
    {
        $id = $this->request->post('id');
        $result = MassMessageLimitLogic::delete($id);
        return AjaxUtils::success($result);
    }
}
