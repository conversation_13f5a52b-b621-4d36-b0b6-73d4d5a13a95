<?php

namespace app\admin\validate\activity_area;

use think\facade\Db;
use app\common\basics\Validate;


class ActivityGoods extends Validate
{
    protected $rule = [
        'review_status' => 'require',
        'description' => 'require',
    ];
    protected $message = [
        'review_status.require' => '请选择审核状态',
        'description.require' => '请填写审核说明',

    ];
    protected $scene = [
        'audit' => ['review_status', 'description'],
        'violation' => ['description'],
    ];
}