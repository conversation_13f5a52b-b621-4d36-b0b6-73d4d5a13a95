<?php
namespace app\admin\logic;

use app\common\basics\Logic;
use app\common\model\MassMessageLimit;
use think\facade\Db;

/**
 * 群发信息限制配置逻辑层
 * Class MassMessageLimitLogic
 * @package app\admin\logic
 */
class MassMessageLimitLogic extends Logic
{
    /**
     * 获取群发信息限制配置列表
     * @return array
     */
    public static function getList()
    {
        return MassMessageLimit::getList();
    }

    /**
     * 添加群发信息限制配置
     * @param array $data
     * @return bool
     */
    public static function add($data)
    {
        return MassMessageLimit::add($data);
    }

    /**
     * 编辑群发信息限制配置
     * @param array $data
     * @return bool
     */
    public static function edit($data)
    {
        return MassMessageLimit::edit($data);
    }

    /**
     * 删除群发信息限制配置
     * @param int $id
     * @return bool
     */
    public static function delete($id)
    {
        return Db::name('MassMessageLimit')->where('id',$id)->delete();
    }

    /**
     * 获取单个群发信息限制配置信息
     * @param int $id
     * @return array
     */
    public static function getInfo($id)
    {
        return MassMessageLimit::getInfo($id);
    }
}
