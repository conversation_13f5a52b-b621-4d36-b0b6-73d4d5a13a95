<?php

namespace app\admin\controller\finance;

use app\admin\logic\agent\AgentLogic;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use think\facade\View;

/**
 * 代理财务管理
 * Class Agent
 * @package app\admin\controller\finance
 */
class Agent extends AdminBase
{
    /**
     * @notes 代理保证金明细
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function depositDetail()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $result = AgentLogic::depositList($params);
            return JsonServer::success('', $result);
        }
        return view('finance/agent/deposit_detail');
    }

    /**
     * @notes 代理佣金明细
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function commissionDetail()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $result = AgentLogic::settle($params);
            return JsonServer::success('', $result);
        }

        $statistics = AgentLogic::statistics();
        View::assign('statistics', $statistics);
        return view('finance/agent/commission_detail');
    }

    /**
     * @notes 代理佣金提现明细
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function withdrawalDetail()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $result = AgentLogic::withdrawalists($params);
            return JsonServer::success('', $result);
        }

        View::assign('summary', AgentLogic::summary());
        View::assign('statistics', AgentLogic::statistics2());
        return view('finance/agent/withdrawal_detail');
    }

    /**
     * @notes 代理保证金详情
     * @return \think\response\View
     * <AUTHOR> @date
     */
    public function depositInfo()
    {
        $id = $this->request->get('id');
        $deposit = AgentLogic::getDepositDetail($id);
        View::assign('deposit', $deposit);
        return view('finance/agent/deposit_info');
    }

    /**
     * @notes 代理佣金详情
     * @return \think\response\View
     * <AUTHOR> @date
     */
    public function commissionInfo()
    {
        $id = $this->request->get('id');
        $commission = AgentLogic::detail(['id' => $id]);
        View::assign('commission', $commission);
        return view('finance/agent/commission_info');
    }

    /**
     * @notes 代理提现详情
     * @return \think\response\View
     * <AUTHOR> @date
     */
    public function withdrawalInfo()
    {
        $id = $this->request->get('id');
        $data = AgentLogic::Agentdetail($id);

        // 合并提现信息和银行/支付宝信息
        $withdrawal = $data['withdrawal'];

        // 根据提现方式添加相应的收款信息
        if ($withdrawal['type'] == 1 && !empty($data['bank'])) {
            // 银行卡提现 - 根据实际数据库字段映射
            $withdrawal['bank_name'] = $data['bank']['name'] ?? '';
            $withdrawal['bank_branch'] = $data['bank']['branch'] ?? '';
            $withdrawal['bank_card'] = $data['bank']['account'] ?? '';
            $withdrawal['bank_user'] = $data['bank']['nickname'] ?? '';
        } elseif ($withdrawal['type'] == 2 && !empty($data['alipay'])) {
            // 支付宝提现 - 根据实际数据库字段映射
            $withdrawal['alipay_account'] = $data['alipay']['account'] ?? '';
            $withdrawal['alipay_name'] = $data['alipay']['username'] ?? '';
        }

        // 添加用户信息
        $withdrawal['user_nickname'] = $data['user']['nickname'] ?? '';
        $withdrawal['user_avatar'] = !empty($data['user']['avatar']) ? UrlServer::getFileUrl($data['user']['avatar']) : '';
        $withdrawal['user_sn'] = $data['user']['sn'] ?? '';

        View::assign('withdrawal', $withdrawal);
        View::assign('user', $data['user']);
        View::assign('bank', $data['bank']);
        View::assign('alipay', $data['alipay']);
        return view('finance/agent/withdrawal_info');
    }

    /**
     * @notes 代理提现统计
     * @return \think\response\Json
     * <AUTHOR> @date
     */
    public function withdrawalStatistics()
    {
        if ($this->request->isAjax()) {
            $statistics = AgentLogic::statistics2();
            return JsonServer::success('获取成功', $statistics);
        }
        return JsonServer::error('请求异常');
    }

    /**
     * @notes 代理保证金统计
     * @return \think\response\Json
     * <AUTHOR> @date
     */
    public function depositStatistics()
    {
        if ($this->request->isAjax()) {
            // 这里可以添加保证金统计逻辑
            $statistics = [
                'total_deposit' => 0,
                'paid_deposit' => 0,
                'refunding_deposit' => 0,
                'refunded_deposit' => 0
            ];
            return JsonServer::success('获取成功', $statistics);
        }
        return JsonServer::error('请求异常');
    }
}
