<?php


namespace app\admin\logic\community;


use app\common\basics\Logic;
use app\common\enum\CommunityLikeEnum;
use app\common\model\community\CommunityExhibition;
use app\common\model\community\CommunityComment;
use app\common\model\community\CommunityExhibitionImage;
use app\common\model\community\CommunityLike;
use app\common\model\community\CommunityTopic;
use app\common\logic\CommunityExhibitionLogic as CommonArticleLogic;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use think\Exception;
use think\facade\Db;


/**
 * 发现逻辑
 * Class CommunityExhibitionLogic
 * @package app\admin\logic\community
 */
class CommunityExhibitionLogic extends Logic
{
    /**
     * @notes 用户列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/3 9:55
     */
    public static function getUserLists($params)
    {
        $where[] = ['del', '=', 0];
        $where[] = ['user_delete', '=', 0];
        // 用户信息
        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $where[] = ['sn|nickname|mobile', 'like', '%'. $params['keyword'] .'%'];
        }
        $lists = User::field('id,sn,nickname,mobile')
            ->where($where)
            ->page($params['page'], $params['limit'])
            ->select()
            ->toArray();
        $count = User::where($where)->count();
        return [
            'count' => $count,
            'lists' => $lists,
        ];
    }

    /**
     * @notes 文章列表
     * @param $get
     * @return array
     * <AUTHOR>
     * @date 2022/5/10 11:07
     */
    public static function lists($get)
    {
        $where = [
            ['a.del', '=', 0]
        ];

        if (!empty($get['keyword'])) {
            $where[] = ['u.sn|u.nickname|u.mobile', 'like', '%' . $get['keyword'] . '%'];
        }

        if (!empty($get['content'])) {
            $where[] = ['a.content', 'like', '%' . $get['content'] . '%'];
        }

        if (isset($get['status']) && $get['status'] != '') {
            $where[] = ['a.status', '=', $get['status']];
        }
        if (isset($get['start_time']) && $get['start_time'] != '') {
            $where[] = ['a.start_time', '>=', strtotime($get['start_time'])];
        }

        if (isset($get['end_time']) && $get['end_time'] != '') {
            $where[] = ['a.end_time', '<=', strtotime($get['end_time'])];
        }

        $model = new CommunityExhibition();
        $lists = $model->with(['images'])->alias('a')
            ->field('a.*,u.nickname,u.avatar,u.mobile')
            ->join('user u', 'u.id = a.user_id')
            ->where($where)
            ->order(['id' => 'desc'])
            ->append(['status_desc'])
            ->paginate([
                'page' => $get['page'],
                'list_rows' => $get['limit'],
                'var_page' => 'page'
            ])
            ->toArray();

        foreach ($lists['data'] as &$item) {
            $item['avatar'] = !empty($item['avatar']) ? UrlServer::getFileUrl($item['avatar']) : '';
            $item['end_time'] = $item['end_time']?date('Y-m-d H:i:s', $item['end_time']):'';
            $item['start_time'] = $item['start_time']?date('Y-m-d H:i:s', $item['start_time']):'';

        }

        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }

    public static function addExhibitionImage($image, $article_id)
    {
        if (!empty($image)) {
            $images = [];
            foreach ($image as $item) {
                $images[] = [
                    'article_id' => $article_id,
                    'image' => $item,
                ];
            }
            (new CommunityExhibitionImage())->saveAll($images);
        }
    }


    /*
     *
     * 获取相似展会
     */
    public static function getSimilarExhibition($id)
    {
        $exhibition = CommunityExhibition::findOrEmpty($id);
        //province_id  city_id  district_id 用或者条件

        $where[]=['province_id','=',$exhibition['province_id']];
        $where[]=['city_id','=',$exhibition['city_id']];
        $where[]=['district_id','=',$exhibition['district_id']];
        $where[]=['user_id','=',$exhibition['user_id']];
        return CommunityExhibition::whereOr($where)->column('id');


    }
    /**
     * @notes 文章详情
     * @param $id
     * @return array
     * <AUTHOR>
     * @date 2022/5/10 16:53
     */
    public static function detail($id)
    {
        $detail = CommunityExhibition::with(['images'])
            ->findOrEmpty($id);
        $detail['start_time']=date('Y-m-d H:i:s',$detail['start_time']);
        $detail['end_time']=date('Y-m-d H:i:s',$detail['end_time']);
        $detail['user_name']=Db::name('user')->where(['id'=>$detail['user_id']])->value('nickname');
        return $detail->toArray();
    }


    /**
     * @notes 删除文章
     * @param $id
     * @return bool
     * <AUTHOR>
     * @date 2022/5/10 16:34
     */
    public static function del($id)
    {
        Db::startTrans();
        try {
            $article = CommunityExhibition::find($id);
            $article->del = 1;
            $article->update_time = time();
            $article->save();

            if (!empty($article['topic_id'])) {
                CommunityTopic::decArticleNum($article['topic_id']);
            }

            Db::commit();
            return true;

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 获取社区配置
     * @return array
     * <AUTHOR>
     * @date 2022/4/28 16:13
     */
    public static function getConfigform()
    {
        $config = [
            'phone' => ConfigServer::get('communityform', 'phone', 1),
            'company' => ConfigServer::get('communityform', 'company', 1),
            'name' => ConfigServer::get('communityform', 'name', 1),
            'wechat_image' => ConfigServer::get('communityform', 'wechat_image', 1),
            'find_image' => ConfigServer::get('communityform', 'find_image', 1),
        ];
        return $config;
    }


    /**
     * @notes 获取商圈配置
     * @return array
     * <AUTHOR>
     * @date 2022/4/28 16:13
     */
    public static function getExhibitionConfig()
    {
        $config = [
            'status' => ConfigServer::get('community', 'status', 1),
            'audit_article' => ConfigServer::get('community', 'audit_article', 1),
            'audit_comment' => ConfigServer::get('community', 'audit_comment', 1),
            'audit_nums' => ConfigServer::get('community', 'audit_nums', 1),
            'audit_video_size' => ConfigServer::get('community', 'audit_video_size', 1),
            'audit_image_size' => ConfigServer::get('community', 'audit_image_size', 1),
            'audit_day_size' => ConfigServer::get('community', 'audit_day_size', 1),
            'commity_image'=>'https://huohanghang.oss-cn-beijing.aliyuncs.com'.Db::name('ad')->where('id',159)->value('image')
        ];
        return $config;
    }


    /**
     * @notes 设置社区配置
     * @param $post
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/4/28 16:14
     */
    public static function setConfig($post)
    {
        ConfigServer::set('communityform', 'phone', $post['phone']);
        ConfigServer::set('communityform', 'company', $post['company']);
        ConfigServer::set('communityform', 'name', $post['name']);
        ConfigServer::set('communityform', 'wechat_image', $post['wechat_image']);
    }
    /**
     * @notes 审核文章
     * @param $post
     * @return bool
     * <AUTHOR>
     * @date 2022/5/12 16:57
     */
    public static function audit($post)
    {
        Db::startTrans();
        try {
            $article = CommunityExhibition::findOrEmpty($post['id']);
            $article->status = $post['status'];
            $article->audit_remark = $post['audit_remark'] ?? '';
            $article->audit_time = time();
            $article->save();

            // 通知粉丝有新文章发布
            CommonArticleLogic::noticeFans($article['user_id'], $post['status']);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 添加文章
     * @Author: 张无忌
     * @param $post
     * @return bool
     */
    public static function add($post)
    {
        try {
            $article =  CommunityExhibition::create([
                'user_id'       => $post['user_id']??0,
                'title'     => $post['title'],
                'site'     => $post['site'] ??'',
                'image'     => $post['images'][0] ??'',
                'wechat_image'     => $post['wechat_image'] ??'',
                'shop_nums'     => $post['shop_nums'] ?? '',
                'can_nums'     => $post['can_nums'] ?? '',
                'person_nums'     => $post['person_nums'] ?? '',
                'start_time'     => $post['start_time'] ?? '',
                'end_time'     => $post['end_time'] ?? '',
                'is_show'     => $post['is_show'],
                'areas'     => $post['areas']??'',
                'province_id'     => $post['province_id']??0,
                'city_id'     => $post['city_id']??0,
                'district_id'     => $post['district_id']??0,
                'content'   => $post['content'] ?? '',
                'address'   => $post['address'] ?? '',
                'longitude'   => $post['longitude'] ?? '',
                'latitude'   => $post['latitude'] ?? '',
                'sort'      => $post['sort'] ?? 0
            ]);
            // 新增文章关联图片
            self::addExhibitionImage($post['images'], $article['id']);
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }



    /**
     * @Notes: 编辑文章
     * @Author: 张无忌
     * @param $post
     * @return bool
     */
    public static function edit($post)
    {
        try {
            CommunityExhibition::update([
                'user_id'       => $post['user_id']??0,
                'title'     => $post['title'],
                'image'     => $post['images'][0] ??'',
                'wechat_image'     => $post['wechat_image'] ??'',
                'site'     => $post['site'] ??'',
                'shop_nums'     => $post['shop_nums'] ?? '',
                'person_nums'     => $post['person_nums'] ?? '',
                'start_time'     => $post['start_time'] ?? '',
                'end_time'     => $post['end_time'] ?? '',
                'is_show'     => $post['is_show'],
                'can_nums'     => $post['can_nums'] ?? '',
                'areas'     => $post['areas']??'',
                'province_id'     => $post['province_id']??0,
                'city_id'     => $post['city_id']??0,
                'district_id'     => $post['district_id']??0,
                'content'   => $post['content'] ?? '',
                'address'   => $post['address'] ?? '',
                'longitude'   => $post['longitude'] ?? '',
                'latitude'   => $post['latitude'] ?? '',
                'sort'      => $post['sort'] ?? 0
            ], ['id'=>$post['id']]);
            // 新增文章关联图片
            (new CommunityExhibitionImage())->destroy(['article_id'=>$post['id']]);
            self::addExhibitionImage($post['images'], $post['id']);
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }
    /**
     * @notes 文章关联评论及点赞
     * @param $get
     * @return array
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2022/5/11 10:14
     */
    public static function getRelationData($get)
    {
        $type = $get['type'] ?? 'comment';
        if ($type == 'comment') {
            $lists = CommunityComment::with(['user'])
                ->where([
                    'del' => 0,
                    'article_id' => $get['id'],
                ])->paginate([
                    'page' => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();
        } else {
            $lists = CommunityLike::with(['user'])
                ->where([
                    'relation_id' => $get['id'],
                    'type' => CommunityLikeEnum::TYPE_ARTICLE
                ])
                ->paginate([
                    'page' => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();
        }
        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }

    public static function getExselect(){
        $model = new CommunityExhibition();
        return  $model->column('title','id');
    }


}