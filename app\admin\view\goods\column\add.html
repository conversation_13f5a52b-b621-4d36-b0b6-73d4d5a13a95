{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-goodsColumn" id="layuiadmin-form-goodsColumn" style="padding: 20px 30px 0 0;">
    <!--标签名称-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span style="color: red">*</span>标签名称:</label>
        <div class="layui-input-inline">
            <input type="text" name="name" lay-verify="required" lay-vertype="tips" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space:nowrap;"><span class="tips">*</span>标签图片：</label>
        <div class="layui-input-block">
            <div class="like-upload-image">
                <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
            </div>
        </div>
    </div>
    <!--标签简介-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span style="color: red">*</span>标签简介:</label>
        <div class="layui-input-inline">
            <textarea name="remark" lay-verify="required"  class="layui-textarea"></textarea>
        </div>
    </div>

    <!--状态-->
    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <input type="checkbox" lay-filter="disable" name="status" lay-skin="switch" lay-text="显示|隐藏">
            <div class=" layui-form-mid layui-word-aux">显示或者隐藏商品标签</div>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="goodsColumn-submit" id="goodsColumn-submit" value="确认">
    </div>
</div>
<script>
    layui.use(["table", "laydate","form"], function(){
        var table   = layui.table;
        var element = layui.element;
        var form = layui.form;
        var laydate = layui.laydate;

        laydate.render({type:'datetime',elem:'#start_time',trigger:'click'});
        laydate.render({type:'datetime',elem:'#end_time',trigger:'click'});
        // 监听 广告位置选择
        form.on('select(selectPid)', function(data){
            var id = data.value;
            var elem = $(data.elem).find("option:selected");
            if(id){
                renderSize(elem);
            }else{
                $('.size-tips-div').hide();
            }

            if(3 == id || 4 == id){
                $('.category').show();
            }else{
                $('.category').hide();
            }

        });
        form.on('radio(link)', function (data) {
            var value = data.value;
            $('.link').hide();
            switch (value) {
                case '1':
                    $('.page').show();
                    break;
                case '2':
                    $('.goods').show();
                    $('.goods-tips').show();
                    break;
                case '3':
                    $('.url').show();
                    $('.url-tips').show();
                    break;
            }

        })
        form.on('radio(adbk)', function (data) {
            var value = data.value;
            $('.link').hide();
            switch (value) {
                case '1':
                    $('.backgroup').show();
                    break;
                case '2':
                    $('.backgroup').hide();
                    break;

            }

        })
        $(document).on('click','.select-goods',function () {
            layer.open({
                type: 2
                ,title: '选择商品'
                ,content: '{:url("common.goods/selectGoods")}'
                ,area: ['90%', '90%']
                ,btn: ['确认', '取消']
                ,yes: function(index, layero){
                    var data = window["layui-layer-iframe" + index].callbackdata();

                    if(data.length){
                        $('#goods_list tbody').remove();
                        var goods = data[0];
                        var goods_html = '<tr>\n' +
                            '                    <td style="text-align: center"><img class="image-show" width="80px" height="80px" src="'+goods.image +'">'+goods.name+'</td>\n' +
                            '                    <td style="text-align: center">'+goods.price+'</td>\n' +
                            '                </tr>';
                        $('#goods_list').prev().val(goods.id);
                        $('#goods_list').append(goods_html);
                        $('.goods').show();
                    }
                }

            })
        })
        function renderSize(elem) {
            var width = elem.attr('data-width') ? elem.attr('data-width'): 0;
            var height = elem.attr('data-height') ? elem.attr('data-height') : 0;
            if(width || height){
                $('.size-tips-div').show();
                var html  = '建议上传广告图片宽*高, '+width+'px*'+height+'px';
                $('.size-tips').text(html);
            }
        }

        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        })

        $(document).on("click", ".add-upload-image2", function () {
            like.imageUpload({
                limit: 1,
                field: "bj_image",
                that: $(this)
            });
        })

    })
</script>