{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--搜索条件-->
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商家名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="shop_name" id="shop_name" placeholder="请输入商家名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">客服名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="kefu_name" id="kefu_name" placeholder="请输入客服名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <select name="status" id="status">
                                <option value="">全部</option>
                                <option value="0">启用</option>
                                <option value="1">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm layuiadmin-btn-admin {$view_theme_color}" lay-submit lay-filter="search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">
                            清空查询
                        </button>
                    </div>
                </div>
            </div>

            <!--操作按钮-->
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-admin {$view_theme_color}" data-type="batchAssign">
                    <i class="layui-icon layui-icon-add-1"></i>批量分配客服
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-warm" data-type="batchRemove">
                    <i class="layui-icon layui-icon-delete"></i>批量移除客服
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-normal" data-type="batchEnable">
                    <i class="layui-icon layui-icon-ok"></i>批量启用
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-danger" data-type="batchDisable">
                    <i class="layui-icon layui-icon-close"></i>批量禁用
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary" data-type="export">
                    <i class="layui-icon layui-icon-export"></i>导出数据
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary" data-type="stats">
                    <i class="layui-icon layui-icon-chart"></i>统计信息
                </button>
            </div>

            <!--表格-->
            <table id="customer-service-lists" lay-filter="customer-service-lists"></table>

            <!--js模板-->
            <script type="text/html" id="status-tpl">
                {{# if(d.disable == 0) { }}
                    <span class="layui-badge layui-bg-green">启用</span>
                {{# } else { }}
                    <span class="layui-badge layui-bg-gray">禁用</span>
                {{# } }}
            </script>

            <script type="text/html" id="tier-level-tpl">
                {{# if(d.tier_level == 0) { }}
                    <span class="layui-badge layui-bg-gray">0元入驻</span>
                {{# } else if(d.tier_level == 1) { }}
                    <span class="layui-badge layui-bg-blue">商家会员</span>
                {{# } else if(d.tier_level == 2) { }}
                    <span class="layui-badge layui-bg-orange">实力厂商</span>
                {{# } else { }}
                    <span class="layui-badge">未知等级</span>
                {{# } }}
            </script>

            <script type="text/html" id="customer-service-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">
                    <i class="layui-icon layui-icon-about"></i>查看
                </a>
                <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="edit">
                    <i class="layui-icon layui-icon-edit"></i>编辑
                </a>
                {{# if(d.disable == 0) { }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">
                        <i class="layui-icon layui-icon-close"></i>禁用
                    </a>
                {{# } else { }}
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="enable">
                        <i class="layui-icon layui-icon-ok"></i>启用
                    </a>
                {{# } }}
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form'], function(){
        var form = layui.form
            ,table = layui.table;

        // 初始化表格
        like.tableLists('#customer-service-lists', '{:url("index")}', [
            {type: 'checkbox', width: 50, fixed: 'left'},
            {field: 'id', width: 80, title: 'ID', sort: true, align: 'center'},
            {field: 'shop_name', width: 150, title: '商家名称', align: 'center'},
            {field: 'tier_level', width: 100, title: '商家等级', align: 'center', templet: '#tier-level-tpl'},
            {field: 'nickname', width: 120, title: '客服昵称', align: 'center'},
            {field: 'account', width: 120, title: '客服账号', align: 'center'},
            {field: 'admin_account', width: 120, title: '管理员账号', align: 'center'},
            {field: 'disable', width: 80, title: '状态', align: 'center', templet: '#status-tpl'},
            {field: 'create_time_text', width: 160, title: '创建时间', align: 'center'},
            {title: '操作', width: 200, align: 'center', fixed: 'right', toolbar: '#customer-service-operation'}
        ]);

        //监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('customer-service-lists', {
                where: field,
                page: {curr: 1},
            });
        });

        //清空查询
        form.on('submit(clear-search)', function () {
            $('#shop_name').val('');
            $('#kefu_name').val('');
            $('#status').val('');
            form.render('select');
            //刷新列表
            table.reload('customer-service-lists', {
                where: [],
                page: {curr: 1},
            });
        });

        //监听工具条
        table.on('tool(customer-service-lists)', function(obj){
            var data = obj.data;
            
            if(obj.event === 'view'){
                layer.open({
                    type: 2,
                    title: '查看客服详情',
                    content: '{:url("view")}?id=' + data.id,
                    area: ['80%', '80%']
                });
            } else if(obj.event === 'edit'){
                layer.open({
                    type: 2,
                    title: '编辑客服',
                    content: '{:url("edit")}?id=' + data.id,
                    area: ['80%', '80%'],
                    btn: ['确定', '取消'],
                    yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index];
                        var submitID = 'editSubmit';
                        var submit = layero.find('iframe').contents().find('#'+ submitID);

                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px',
                                            icon: 1,
                                            time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('customer-service-lists');
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            } else if(obj.event === 'enable' || obj.event === 'disable'){
                var status = obj.event === 'disable' ? 1 : 0;
                var text = obj.event === 'disable' ? '禁用' : '启用';
                
                layer.confirm('确定' + text + '该客服？', function(index){
                    like.ajax({
                        url:'{:url("batchSetStatus")}',
                        data:{kefu_ids: [data.id], status: status},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px',
                                    icon: 1,
                                    time: 1000
                                });
                                table.reload('customer-service-lists');
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });

        //事件
        var active = {
            batchAssign: function(){
                var checkStatus = table.checkStatus('customer-service-lists');
                if(checkStatus.data.length === 0){
                    layer.msg('请选择要操作的数据');
                    return;
                }
                
                layer.open({
                    type: 2,
                    title: '批量分配客服',
                    content: '{:url("batchAssign")}',
                    area: ['80%', '80%'],
                    btn: ['确定', '取消'],
                    yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index];
                        var submitID = 'batchAssignSubmit';
                        var submit = layero.find('iframe').contents().find('#'+ submitID);

                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            var shopIds = [];
                            checkStatus.data.forEach(function(item){
                                shopIds.push(item.shop_id);
                            });
                            field.shop_ids = shopIds;
                            
                            like.ajax({
                                url:'{:url("batchAssign")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px',
                                            icon: 1,
                                            time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('customer-service-lists');
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            },
            batchRemove: function(){
                var checkStatus = table.checkStatus('customer-service-lists');
                if(checkStatus.data.length === 0){
                    layer.msg('请选择要操作的数据');
                    return;
                }
                
                layer.confirm('确定批量移除选中的客服？', function(index){
                    var shopIds = [];
                    var kefuIds = [];
                    checkStatus.data.forEach(function(item){
                        shopIds.push(item.shop_id);
                        kefuIds.push(item.id);
                    });
                    
                    like.ajax({
                        url:'{:url("batchRemove")}',
                        data:{shop_ids: shopIds, kefu_ids: kefuIds},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px',
                                    icon: 1,
                                    time: 1000
                                });
                                table.reload('customer-service-lists');
                            }
                        }
                    });
                    layer.close(index);
                });
            },
            batchEnable: function(){
                var checkStatus = table.checkStatus('customer-service-lists');
                if(checkStatus.data.length === 0){
                    layer.msg('请选择要操作的数据');
                    return;
                }
                
                layer.confirm('确定批量启用选中的客服？', function(index){
                    var kefuIds = [];
                    checkStatus.data.forEach(function(item){
                        kefuIds.push(item.id);
                    });
                    
                    like.ajax({
                        url:'{:url("batchSetStatus")}',
                        data:{kefu_ids: kefuIds, status: 0},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px',
                                    icon: 1,
                                    time: 1000
                                });
                                table.reload('customer-service-lists');
                            }
                        }
                    });
                    layer.close(index);
                });
            },
            batchDisable: function(){
                var checkStatus = table.checkStatus('customer-service-lists');
                if(checkStatus.data.length === 0){
                    layer.msg('请选择要操作的数据');
                    return;
                }
                
                layer.confirm('确定批量禁用选中的客服？', function(index){
                    var kefuIds = [];
                    checkStatus.data.forEach(function(item){
                        kefuIds.push(item.id);
                    });
                    
                    like.ajax({
                        url:'{:url("batchSetStatus")}',
                        data:{kefu_ids: kefuIds, status: 1},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px',
                                    icon: 1,
                                    time: 1000
                                });
                                table.reload('customer-service-lists');
                            }
                        }
                    });
                    layer.close(index);
                });
            },
            export: function(){
                like.ajax({
                    url:'{:url("export")}',
                    data: table.cache['customer-service-lists'],
                    type:"get",
                    success:function(res)
                    {
                        if(res.code == 1) {
                            layui.layer.msg('导出成功', {
                                offset: '15px',
                                icon: 1,
                                time: 1000
                            });
                            // 这里可以添加下载逻辑
                        }
                    }
                });
            },
            stats: function(){
                like.ajax({
                    url:'{:url("getStats")}',
                    type:"get",
                    success:function(res)
                    {
                        if(res.code == 1) {
                            var data = res.data;
                            var content = '<div style="padding: 20px;">' +
                                '<p>总客服数：' + data.total_kefu + '</p>' +
                                '<p>启用客服数：' + data.enabled_kefu + '</p>' +
                                '<p>禁用客服数：' + data.disabled_kefu + '</p>' +
                                '<p>有客服的商家数：' + data.shop_with_kefu + '</p>' +
                                '<p>总商家数：' + data.total_shop + '</p>' +
                                '<p>客服覆盖率：' + data.coverage_rate + '%</p>' +
                                '</div>';
                            
                            layer.open({
                                type: 1,
                                title: '客服统计信息',
                                content: content,
                                area: ['400px', '300px']
                            });
                        }
                    }
                });
            }
        };
        
        $('.layui-btn.layuiadmin-btn-admin').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
