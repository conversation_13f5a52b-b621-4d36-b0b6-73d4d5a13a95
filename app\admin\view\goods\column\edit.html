{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-goodsColumn" id="layuiadmin-form-goodsColumn" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$detail.id}" name="id">
    <!--标签名称-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span style="color: red">*</span>标签名称:</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="{$detail.name}" lay-verify="required" lay-vertype="tips" autocomplete="off" class="layui-input">
        </div>
    </div>

    <!--标签简介-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span style="color: red">*</span>标签简介:</label>
        <div class="layui-input-inline">
            <textarea name="remark" lay-verify="required"  class="layui-textarea">{$detail.remark}</textarea>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space:nowrap;"><span class="tips">*</span>标签图片：</label>
        <div class="layui-input-block">
            <div class="like-upload-image">
                {if $detail.image}
                <div class="upload-image-div">
                    <img src="{$detail.image}" alt="img">
                    <input type="hidden" name="image" value="{$detail.image}">
                    <div class="del-upload-btn">x</div>
                </div>
                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image"> + 添加图片</a></div>
                {else}
                <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                {/if}
            </div>
        </div>
    </div>
    <!--状态-->
    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <input type="checkbox" lay-filter="disable" name="status" lay-skin="switch" lay-text="显示|隐藏" {if condition="$detail.status eq 1" }checked{/if}>
            <div class=" layui-form-mid layui-word-aux">显示或者隐藏商品标签</div>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="goodsColumn-submit-edit" id="goodsColumn-submit-edit" value="确认">
    </div>
</div>
<script>
    layui.use(["table", "laydate","form", "jquery"], function(){
        var $ = layui.jquery;
        var table   = layui.table;
        var element = layui.element;
        var form = layui.form;
        var laydate = layui.laydate;



        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        })
        $(document).on("click", ".add-upload-image2", function () {
            like.imageUpload({
                limit: 1,
                field: "bj_image",
                that: $(this)
            });
        })


    })
</script>