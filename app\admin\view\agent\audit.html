{layout name="layout2" /}
<style>
    .image {
        height:60px;width: 60px;margin-right: 5px;
    }
</style>

<div class="layui-card layui-form" style="padding-bottom: 10%">
    <div class="layui-card-body">

        <input type="hidden" name="id" value="{$detail.detal.id}">
        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>佣金状态：</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="冻结" {if $detail.detal.freeze_status}checked{/if}>
                <input type="radio" name="status" value="0" title="解冻" {if !$detail.detal.freeze_status}checked{/if}>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>审核说明：</label>
            <div class="layui-input-block" style="width: 50%">
                <textarea class="layui-textarea"  name="remark"></textarea>
            </div>
        </div>

        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
        </div>
    </div>
</div>


<script>
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form'], function () {
        var form = layui.form;

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });
    });
</script>