<?php


namespace app\admin\controller\shop;


use app\admin\logic\shop\TreatyLogic;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;

/**
 * 入驻协议
 * Class Treaty
 * @package app\admin\controller\shop
 */
class Treaty extends AdminBase
{
    public function index()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = TreatyLogic::set($post);
            if ($res === false) {
                $error = TreatyLogic::getError() ?: '更新失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('更新成功');
        }

        return view('', [
            'detail' => TreatyLogic::detail()
        ]);
    }
}