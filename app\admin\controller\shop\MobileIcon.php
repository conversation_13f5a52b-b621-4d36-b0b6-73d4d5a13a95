<?php

namespace app\admin\controller\shop;

use app\admin\logic\shop\MobileIconLogic;
use app\admin\validate\shop\MobileIconValidate;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use think\facade\View;

/**
 * 移动端图标管理
 * Class MobileIcon
 * @package app\admin\controller\shop
 */
class MobileIcon extends AdminBase
{
    /**
     * Notes: 列表
     * @return string|\think\response\Json
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = MobileIconLogic::lists($get);
            return JsonServer::success('获取成功', $lists);
        }
        return view();
    }

    /**
     * Notes: 添加
     * @return string|\think\response\Json
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new MobileIconValidate())->goCheck('add');
            if (MobileIconLogic::add($post)) {
                return JsonServer::success('添加成功');
            }
            return JsonServer::error(MobileIconLogic::getError() ?: '添加失败');
        }

        // 获取商家权限列表
        $authList = MobileIconLogic::getShopAuthList();
        // 获取商家类型选项
        $merchantTypeOptions = MobileIconLogic::getMerchantTypeOptions();
        return view('', ['authList' => $authList, 'merchantTypeOptions' => $merchantTypeOptions]);
    }

    /**
     * Notes: 编辑
     * @return string|\think\response\Json
     */
    public function edit()
    {
        $id = $this->request->get('id');
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new MobileIconValidate())->goCheck('edit');
            if (MobileIconLogic::edit($post)) {
                return JsonServer::success('编辑成功');
            }
            return JsonServer::error(MobileIconLogic::getError() ?: '编辑失败');
        }

        $detail = MobileIconLogic::detail($id);
        $authList = MobileIconLogic::getShopAuthList();
        // 获取商家类型选项
        $merchantTypeOptions = MobileIconLogic::getMerchantTypeOptions();
        return view('', ['detail' => $detail, 'authList' => $authList, 'merchantTypeOptions' => $merchantTypeOptions]);
    }

    /**
     * Notes: 删除
     * @return \think\response\Json
     */
    public function del()
    {
        $ids = $this->request->post('ids');
        if (MobileIconLogic::del($ids)) {
            return JsonServer::success('删除成功');
        }
        return JsonServer::error(MobileIconLogic::getError() ?: '删除失败');
    }

    /**
     * Notes: 状态切换
     * @return \think\response\Json
     */
    public function status()
    {
        $post = $this->request->post();
        if (MobileIconLogic::status($post)) {
            return JsonServer::success('操作成功');
        }
        return JsonServer::error(MobileIconLogic::getError() ?: '操作失败');
    }

    /**
     * Notes: 更新排序
     * @return \think\response\Json
     */
    public function updateSort()
    {
        $post = $this->request->post();
        if (MobileIconLogic::updateSort($post)) {
            return JsonServer::success('排序更新成功');
        }
        return JsonServer::error(MobileIconLogic::getError() ?: '更新失败');
    }

    /**
     * Notes: 更新字段
     * @return \think\response\Json
     */
    public function updateField()
    {
        $post = $this->request->post();
        if (MobileIconLogic::updateField($post)) {
            return JsonServer::success('更新成功');
        }
        return JsonServer::error(MobileIconLogic::getError() ?: '更新失败');
    }
}
