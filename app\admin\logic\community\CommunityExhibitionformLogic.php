<?php


namespace app\admin\logic\community;


use app\common\basics\Logic;
use app\common\enum\CommunityLikeEnum;
use app\common\model\community\CommunityExhibitionform;
use app\common\model\community\CommunityComment;
use app\common\model\community\CommunityExhibitionImage;
use app\common\model\community\CommunityLike;
use app\common\model\community\CommunityTopic;
use app\common\logic\CommunityExhibitionLogic as CommonArticleLogic;
use app\common\server\UrlServer;
use think\Exception;
use think\facade\Db;
use app\common\server\ExportExcelServer;


/**
 * 发现逻辑
 * Class CommunityExhibitionLogic
 * @package app\admin\logic\community
 */
class CommunityExhibitionformLogic extends Logic
{

    /**
     * @notes 文章列表
     * @param $get
     * @return array
     * <AUTHOR>
     * @date 2022/5/10 11:07
     */
    public static function lists($get)
    {
        $where = [
            ['a.del', '=', 0]
        ];

        if (!empty($get['keyword'])) {
            $where[] = ['u.sn|u.nickname|u.mobile', 'like', '%' . $get['keyword'] . '%'];
        }

        if (!empty($get['phone'])) {
            $where[] = ['a.phone','=', $get['phone']];
        }

        if (!empty($get['exhibition_id'])) {
            $where[] = ['a.exhibition_id','=', $get['exhibition_id']];
        }
        if (isset($get['type_id']) && $get['type_id']!==null) {
            $where[] = ['a.type_id','=', $get['type_id']];
        }

        if (isset($get['start_time']) && $get['start_time'] != '') {
            $where[] = ['a.create_time', '>=', strtotime($get['start_time'])];
        }

        if (isset($get['end_time']) && $get['end_time'] != '') {
            $where[] = ['a.create_time', '<=', strtotime($get['end_time'])];
        }

        $model = new CommunityExhibitionform();
        $lists = $model->with(['exinfo'])->alias('a')
            ->field('a.*,u.nickname,u.avatar,u.sn')
            ->join('user u', 'u.id = a.user_id')
            ->where($where)
            ->order(['id' => 'desc'])
            ->paginate([
                'page' => $get['page'],
                'list_rows' => $get['limit'],
                'var_page' => 'page'
            ])
            ->toArray();

        foreach ($lists['data'] as &$item) {
            $item['avatar'] = !empty($item['avatar']) ? UrlServer::getFileUrl($item['avatar']) : '';
            $item['title'] = $item['exinfo']['title']?? '';
            $item['sex'] = $item['sex']?'女': '男';
            $item['type_id'] = $item['type_id']?'展位咨询': '观展登记';
//            $item['end_time'] = $item['end_time']?date('Y-m-d H:i:s', $item['end_time']):'';
//            $item['start_time'] = $item['start_time']?date('Y-m-d H:i:s', $item['start_time']):'';

        }

        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }

    /**
     * @notes 积分明细
     * @param $get
     * @return array
     * <AUTHOR>
     * @date 2022/2/22 5:59 下午
     */
    public static function integral($get, $is_export = false,$d_type=0)
    {
        $where = [
            ['a.del', '=', 0]
        ];

        if (!empty($get['keyword'])) {
            $where[] = ['u.sn|u.nickname|u.mobile', 'like', '%' . $get['keyword'] . '%'];
        }

        if (!empty($get['phone'])) {
            $where[] = ['a.phone','=', $get['phone']];
        }

        if (!empty($get['exhibition_id'])) {
            $where[] = ['a.exhibition_id','=', $get['exhibition_id']];
        }
        if (isset($get['type_id']) && $get['type_id']!==null) {
            $where[] = ['a.type_id','=', $get['type_id']];
        }

        if (isset($get['start_time']) && $get['start_time'] != '') {
            $where[] = ['a.create_time', '>=', strtotime($get['start_time'])];
        }

        if (isset($get['end_time']) && $get['end_time'] != '') {
            $where[] = ['a.create_time', '<=', strtotime($get['end_time'])];
        }

        // 导出
        if (true === $is_export) {
            return self::export($where,$d_type);
        }


    }
    /**
     * @notes 导出商家账户明细Excel
     * @param array $where
     * @return array|false
     * <AUTHOR>
     * @date 2022/4/24 10:10
     */
    public static function export($where,$d_type=0)
    {
        try {
            $model = new CommunityExhibitionform();
            $lists = $model->with(['exinfo'])->alias('a')
                ->field('a.*,u.nickname,u.avatar,u.sn')
                ->join('user u', 'u.id = a.user_id')
                ->where($where)
                ->order(['id' => 'desc'])
                ->select()
                ->toArray();

            foreach ($lists as &$item) {
//                $item['avatar'] = !empty($item['avatar']) ? UrlServer::getFileUrl($item['avatar']) : '';
                $item['title'] = $item['exinfo']['title']?? '';
                $item['sex'] = $item['sex']?'女': '男';
                $item['type_id'] = $item['type_id']?'展位咨询': '观展登记';
            }

            $excelFields = [
                'name' => '用户姓名',
                'nickname' => '会员昵称',
                'phone' => '联系方式',
                'sex' => '性别',
                'title' => '登记展会',
                'company' => '企业名称',
                'create_time' => '提交时间',
            ];

            $export = new ExportExcelServer();
            $export->setFileName('展会登记信息');
            $result = $export->createExcel($excelFields, $lists,$d_type);

            return ['url' => $result];

        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function addExhibitionImage($image, $article_id)
    {
        if (!empty($image)) {
            $images = [];
            foreach ($image as $item) {
                $images[] = [
                    'article_id' => $article_id,
                    'image' => $item,
                ];
            }
            (new CommunityExhibitionImage())->saveAll($images);
        }
    }
    /**
     * @notes 文章详情
     * @param $id
     * @return array
     * <AUTHOR>
     * @date 2022/5/10 16:53
     */
    public static function detail($id)
    {
        $detail = CommunityExhibitionform::with(['exinfo'])->alias('a')
            ->field('a.*,u.nickname,u.avatar,u.sn')
            ->join('user u', 'u.id = a.user_id')
            ->findOrEmpty($id);
        $detail['avatar'] = !empty($detail['avatar']) ? UrlServer::getFileUrl($detail['avatar']) : '';
        $detail['title'] = $detail['exinfo']['title']?? '';
        $detail['sex'] = $detail['sex']?'女': '男';
        $detail['type_id'] = $detail['type_id']?'展位咨询': '观展登记';
        return $detail->toArray();
    }


    /**
     * @notes 删除文章
     * @param $id
     * @return bool
     * <AUTHOR>
     * @date 2022/5/10 16:34
     */
    public static function del($id)
    {
        Db::startTrans();
        try {
            $article = CommunityExhibition::find($id);
            $article->del = 1;
            $article->update_time = time();
            $article->save();

            if (!empty($article['topic_id'])) {
                CommunityTopic::decArticleNum($article['topic_id']);
            }

            Db::commit();
            return true;

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


}