{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-admin" id="layuiadmin-form-admin" style="padding: 20px 30px 0 0;">
    
    <div class="layui-form-item">
        <label class="layui-form-label">选择客服</label>
        <div class="layui-input-block">
            <div id="kefu-list" style="max-height: 300px; overflow-y: auto; border: 1px solid #e6e6e6; padding: 10px;">
                {volist name="kefu_list" id="kefu"}
                <div style="margin-bottom: 10px;">
                    <input type="checkbox" name="kefu_ids[]" value="{$kefu.id}" title="{$kefu.nickname} ({$kefu.account}) - {$kefu.shop_name}" lay-skin="primary">
                </div>
                {/volist}
            </div>
            <div class="layui-form-mid layui-word-aux">选择要分配的客服人员</div>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">分配说明</label>
        <div class="layui-input-block">
            <textarea name="remark" placeholder="请输入分配说明（可选）" class="layui-textarea"></textarea>
        </div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="batchAssignSubmit" id="batchAssignSubmit">确认分配</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</div>

<script>
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form'], function () {
        var form = layui.form;
        var $ = layui.jquery;

        // 监听提交
        form.on('submit(batchAssignSubmit)', function(data) {
            var field = data.field;
            
            // 验证是否选择了客服
            if (!field['kefu_ids[]'] || field['kefu_ids[]'].length === 0) {
                layer.msg('请至少选择一个客服', {icon: 2});
                return false;
            }
            
            // 处理数组格式
            if (typeof field['kefu_ids[]'] === 'string') {
                field.kefu_ids = [field['kefu_ids[]']];
            } else {
                field.kefu_ids = field['kefu_ids[]'];
            }
            delete field['kefu_ids[]'];

            like.ajax({
                url: '{:url("batchAssign")}',
                data: field,
                type: "post",
                success: function(res) {
                    if (res.code == 1) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function() {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                }
            });
            return false;
        });
    });
</script>
