{layout name="layout1" /}
<style>
    .layui-table-cell {
        height:auto;
    }
    .goods-content>div:not(:last-of-type) {
        bsettlement-bottom:1px solid #DCDCDC;
    }
    .goods-data::after{
        display: block;
        content: '';
        clear: both;
    }
    .goods_name_hide{
        overflow:hidden;
        white-space:nowrap;
        text-overflow: ellipsis;
    }
    .operation-btn {
        margin: 5px;
    }
    .table-operate{
        text-align: left;
        font-size:14px;
        padding:0 5px;
        height:auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-break: break-all;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="bsettlement:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*查看商家订单结算的情况。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算汇总 -->
        <h2 style="margin: 20px;">结算汇总</h2>
        <div style="margin: 0 20px">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算成交订单数</div>
                        <div class="layui-card-body"><p>{$statistics.settleOrederNum}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算营业额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleOrederAmount}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">待结算营业额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleOrederAmountWait}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算分销佣金金额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleDistributionAmount}</p></div>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算入账金额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleWithdrawalAmount}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算交易服务费</div>
                        <div class="layui-card-body"><p>￥{$statistics.settlePoundageAmount}</p></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算记录 -->
        <h2 style="margin: 20px;">结算记录</h2>
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="name" class="layui-form-label">商家名称：</label>
                    <div class="layui-inline">
                        <div class="layui-input-inline" >
                            <input type="text" id="name" name="name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">结算时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="start_time" name="start_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">至</div>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" style="margin-right:0;">
                            <input type="text" id="end_time" name="end_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="data-export">导出</a>
                </div>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="like-table-lists" lay-filter="like-table-lists"></table>
                <script type="text/html" id="table-storeInfo">
                    <img src="{{d.logo}}" alt="图标" style="width:60px;height:60px;margin-right:5px;">
                    <div class="layui-inline" style="text-align:left;">
                        <p>商家编号：{{d.shop_id}}</p>
                        <p>商家名称：{{d.name}}</p>
                        <p>商家类型：{{d.type}}</p>
                    </div>
                </script>
                <script type="text/html" id="table-operation">
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-event="record">结算详细</a>
                </script>
            </div>
        </div>

    </div>
</div>

<script>
    layui.use(["form"], function() {
        var $ = layui.$;
        var form = layui.form;
        var table = layui.table;
        var laydate = layui.laydate;

        laydate.render({type:"datetime", elem:"#start_time", trigger:"click"});
        laydate.render({type:"datetime", elem:"#end_time", trigger:"click"});

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"id", width:60, title:"ID"}
            ,{field:"store", width:250, align:"center", title:"商家信息", templet:"#table-storeInfo"}
            ,{field:"deal_order_count", width:150, align:"center", title:"已结算成交订单数"}
            ,{field:"business_money", width:150, align:"center",title:"已结算营业额"}
            ,{title:"操作", width:140, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);

        var active = {
            record: function (obj) {
                layer.open({
                    type: 2
                    ,title: "结算记录"
                    ,content: "{:url('finance.Shop/settlementRecord')}?shop_id="+obj.data.shop_id
                    ,area: ["90%", "90%"]
                });
            }
        };
        like.eventClick(active);

        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        form.on("submit(clear-search)", function(){
            $("#start_time").val("");
            $("#end_time").val("");
            $("#name").val("");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });


        // 导出
        form.on('submit(data-export)', function (data) {
            var field = data.field;
            like.ajax({
                url: '{:url("finance.Shop/settlementExport")}'
                , data: field
                , type: 'get'
                , success: function (res) {
                    if (res.code == 1) {
                        window.location.href = res.data.url;
                    }
                }
            });
        });

    });
</script>