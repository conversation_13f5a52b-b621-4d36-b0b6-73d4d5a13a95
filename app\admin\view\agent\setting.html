{layout name="layout1" /}
<style>
    .agent-setting-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px;
    }

    .setting-header {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    }

    .setting-header h1 {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #2c3e50;
    }

    .setting-header p {
        font-size: 16px;
        color: #666;
        margin: 0;
    }

    .setting-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .card-header {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        padding: 20px 30px;
        font-size: 18px;
        font-weight: 600;
        border-bottom: none;
    }

    .card-body {
        padding: 30px;
    }

    .layui-form-item {
        margin-bottom: 25px;
        position: relative;
    }

    .layui-form-label {
        width: 160px;
        font-weight: 600;
        color: #333;
        font-size: 14px;
        line-height: 38px;
        padding-right: 20px;
    }

    .layui-input-block {
        margin-left: 180px;
    }

    .layui-input, .layui-select {
        border: 2px solid #e8e8e8;
        border-radius: 8px;
        padding: 10px 15px;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .layui-input:focus, .layui-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .upload-section {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .upload-section:hover {
        border-color: #3498db;
        background: #e3f2fd;
    }

    .upload-image-div {
        display: inline-block;
        position: relative;
        border: 2px solid #e8e8e8;
        border-radius: 12px;
        overflow: hidden;
        margin: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .upload-image-div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .upload-image-div img {
        width: 120px;
        height: 90px;
        object-fit: cover;
        display: block;
    }

    .upload-image-elem {
        display: inline-block;
        margin: 10px;
    }

    .add-upload-image {
        display: inline-block;
        padding: 40px 30px;
        border: 2px dashed #3498db;
        border-radius: 12px;
        color: #3498db;
        text-decoration: none;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        background: white;
        min-width: 120px;
        text-align: center;
    }

    .add-upload-image:hover {
        background: #3498db;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
    }

    .del-upload-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 24px;
        height: 24px;
        background: #ff4757;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
    }

    .del-upload-btn:hover {
        background: #ff3742;
        transform: scale(1.1);
    }

    .form-tips {
        color: #666;
        font-size: 13px;
        margin-top: 8px;
        padding: 10px 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #3498db;
    }

    .submit-section {
        text-align: center;
        padding: 30px;
        background: white;
        border-radius: 15px;
        margin-top: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    }

    .layui-btn-normal {
        background: linear-gradient(45deg, #3498db, #2980b9);
        border: none;
        border-radius: 25px;
        padding: 2px 20px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .layui-btn-normal:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
    }

    .setting-group {
        background: white;
        border-radius: 12px;
        margin-bottom: 20px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    }

    .group-title {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        padding: 15px 25px;
        font-size: 16px;
        font-weight: 600;
        margin: 0;
    }

    .group-content {
        padding: 25px;
    }

    .input-with-unit {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .input-with-unit .layui-input {
        flex: 1;
    }

    .unit-label {
        color: #3498db;
        font-weight: 600;
        font-size: 14px;
        min-width: 30px;
    }

    .debug-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 6px;
        padding: 10px 15px;
        margin-top: 10px;
        font-size: 12px;
        color: #856404;
    }
</style>
<div class="agent-setting-container">
    <!-- 页面标题 -->
    <div class="setting-header">
        <h1>🏢 代理系统设置</h1>
        <p>配置代理佣金比例、保证金、提现手续费等核心参数</p>
    </div>

    <!-- 表单区域 -->
    <div class="layui-form">
        <!-- 佣金设置组 -->
        <div class="setting-group">
            <h3 class="group-title">💰 佣金比例设置</h3>
            <div class="group-content">
                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">区域限制：</label>
                    <div class="layui-input-block">
                        <input type="checkbox" value="1" name="area_xz" lay-skin="switch" lay-text="ON|OFF" {if $config.area_xz == 1}checked{/if}>
                    </div>
                </div>

                <!-- 集采购会员代理佣金设置 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">集采购会员代理直推比率：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="purchase_direct_ratio" class="layui-input" value="{$config.purchase_direct_ratio}" min="0" max="100" step="0.01">
                            <span class="unit-label">%</span>
                        </div>
                        <div class="form-tips">集采购会员代理获得直推用户消费额的佣金比例</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">集采购会员代理间推比率：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="purchase_indirect_ratio" class="layui-input" value="{$config.purchase_indirect_ratio}" min="0" max="100" step="0.01">
                            <span class="unit-label">%</span>
                        </div>
                        <div class="form-tips">集采购会员代理获得间推用户消费额的佣金比例</div>
                    </div>
                </div>

                <!-- 商家会员代理佣金设置 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">商家会员代理直推比率：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="merchant_direct_ratio" class="layui-input" value="{$config.merchant_direct_ratio}" min="0" max="100" step="0.01">
                            <span class="unit-label">%</span>
                        </div>
                        <div class="form-tips">商家会员代理获得直推用户消费额的佣金比例</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">商家会员代理间推比率：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="merchant_indirect_ratio" class="layui-input" value="{$config.merchant_indirect_ratio}" min="0" max="100" step="0.01">
                            <span class="unit-label">%</span>
                        </div>
                        <div class="form-tips">商家会员代理获得间推用户消费额的佣金比例</div>
                    </div>
                </div>

                <!-- 验厂验商代理佣金设置 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">验厂验商代理直推比率：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="inspection_direct_ratio" class="layui-input" value="{$config.inspection_direct_ratio}" min="0" max="100" step="0.01">
                            <span class="unit-label">%</span>
                        </div>
                        <div class="form-tips">验厂验商代理获得直推用户费用的佣金比例</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">验厂验商代理间推比率：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="inspection_indirect_ratio" class="layui-input" value="{$config.inspection_indirect_ratio}" min="0" max="100" step="0.01">
                            <span class="unit-label">%</span>
                        </div>
                        <div class="form-tips">验厂验商代理获得间推用户费用的佣金比例</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算设置组 -->
        <div class="setting-group">
            <h3 class="group-title">⏰ 结算与费用设置</h3>
            <div class="group-content">

                <div class="layui-form-item">
                    <label class="layui-form-label">佣金结算期：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="js_date" value="{$config.js_date}" lay-verify="required" lay-verType="tips" placeholder="请输入天数" min="0" autocomplete="off" class="layui-input">
                            <span class="unit-label">天</span>
                        </div>
                        <div class="form-tips">佣金从订单完成后多少天开始结算</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">代理保证金：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="agent_deposit_amount" value="{$config.agent_deposit_amount ?? 0.01}" lay-verify="required|number" lay-verType="tips" placeholder="请输入金额" min="0.01" step="0.01" autocomplete="off" class="layui-input">
                            <span class="unit-label">元</span>
                        </div>
                        <div class="form-tips">代理商需要缴纳的保证金金额</div>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">保证金公示期：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="deposit_publicity_period_days" value="{$config.deposit_publicity_period_days ?? 0}" lay-verify="required|number" lay-verType="tips" placeholder="请输入天数" min="0" autocomplete="off" class="layui-input">
                            <span class="unit-label">天</span>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">申请后公示期：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="refund_publicity_period_days" value="{$config.refund_publicity_period_days ?? 90}" lay-verify="required|number" lay-verType="tips" placeholder="请输入天数" min="0" autocomplete="off" class="layui-input">
                            <span class="unit-label">天</span>
                        </div>
                        <div class="form-tips">用户端点击申请退款后的公示期时长，默认为90天</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">提现手续费：</label>
                    <div class="layui-input-block">
                        <div class="input-with-unit">
                            <input type="number" name="withdrawal_fee" value="{$config.withdrawal_fee}" lay-verify="required" lay-verType="tips" placeholder="请输入百分比" min="0" max="100" step="0.1" autocomplete="off" class="layui-input">
                            <span class="unit-label">%</span>
                        </div>
                        <div class="form-tips">代理商提现时收取的手续费比例</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片设置组 -->
        <div class="setting-group">
            <h3 class="group-title">🖼️ 图片素材设置</h3>
            <div class="group-content">



                <div class="layui-form-item">
                    <label class="layui-form-label">代理页面背景图：</label>
                    <div class="layui-input-block">
                        <div class="upload-section">
                            {if !empty($config.agnet_bg_image)}
                            <div class="upload-image-div">
                                <img src="{$config.agnet_bg_image}" alt="代理页面背景图">
                                <input name="agnet_bg_image" type="hidden" value="{$config.agnet_bg_image}">
                                <div class="del-upload-btn">×</div>
                            </div>
                            <div class="upload-image-elem" style="display:none;"><a class="add-upload-image h5_share_image">+ 添加图片</a></div>
                            {else}
                            <div class="upload-image-elem"><a class="add-upload-image h5_share_image">+ 添加图片</a></div>
                            {/if}
                        </div>
                        <div class="form-tips">用于代理页面的背景图片，建议尺寸：1920×1080px</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">退保证金警告图：</label>
                    <div class="layui-input-block">
                        <div class="upload-section">
                            {if isset($config.agent_deposit_refund_warning_image) && $config.agent_deposit_refund_warning_image != ''}
                            <div class="upload-image-div">
                                <img src="{$config.agent_deposit_refund_warning_image}" alt="退保证金警告图">
                                <input name="agent_deposit_refund_warning_image" type="hidden" value="{$config.agent_deposit_refund_warning_image}">
                                <div class="del-upload-btn">×</div>
                            </div>
                            <div class="upload-image-elem" style="display:none;"><a class="add-upload-image warning_image_upload">+ 添加图片</a></div>
                            {else}
                            <div class="upload-image-elem"><a class="add-upload-image warning_image_upload">+ 添加图片</a></div>
                            {/if}
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- 说明文本设置组 -->
        <div class="setting-group">
            <h3 class="group-title">📝 说明文本设置</h3>
            <div class="group-content">

                <div class="layui-form-item">
                    <label class="layui-form-label">可提现佣金说明：</label>
                    <div class="layui-input-block">
                        <input type="text" name="ktx_explain" value="{$config.ktx_explain}" id="ktx_explain" autocomplete="off" class="layui-input" placeholder="请输入可提现佣金的说明文字">
                        <div class="form-tips">在代理页面显示的可提现佣金说明文字</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">待结算佣金说明：</label>
                    <div class="layui-input-block">
                        <input type="text" name="djs_explain" value="{$config.djs_explain}" id="djs_explain" autocomplete="off" class="layui-input" placeholder="请输入待结算佣金的说明文字">
                        <div class="form-tips">在代理页面显示的待结算佣金说明文字</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">已冻结佣金说明：</label>
                    <div class="layui-input-block">
                        <input type="text" name="ydj_explain" value="{$config.ydj_explain}" id="ydj_explain" autocomplete="off" class="layui-input" placeholder="请输入已冻结佣金的说明文字">
                        <div class="form-tips">在代理页面显示的已冻结佣金说明文字</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="set">
                💾 保存设置
            </button>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form','element'], function(){
        var $ = layui.$,form = layui.form,element = layui.element;

        like.delUpload();

        // 代理页面背景图上传
        $(document).on("click", ".h5_share_image", function () {
            like.imageUpload({
                limit: 1,
                field: "agnet_bg_image",
                that: $(this)
            });
        });

        // 代理退保证金警告图上传
        $(document).on("click", ".warning_image_upload", function () {
            like.imageUpload({
                limit: 1,
                field: "agent_deposit_refund_warning_image",
                that: $(this)
            });
        });
        form.on('submit(set)', function(data) {
            like.ajax({
                url:'{:url("agent.agent/setting")}',
                data: data.field,
                type:"post",
                success:function(res)
                {
                    if(res.code == 1)
                    {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        });
                    }
                }
            });
        });

    });
</script>
