{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto;
    }
    .layui-card-body .search{
        margin: 15px 0px 0px -33px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台管理广告位信息，系统默认了部分广告位，允许新建广告位。</p>
                        <p>*移动端商城含H5、小程序、APP。</p>
                        <p>*广告链接可以选择商城页面，商品页面，自定义链接，用户点击广告后跳转到相应的页面。</p>
                        <p>*广告停用之后，商城不展示该广告。</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 主体区域 -->
        <div class="layui-tab layui-tab-card" lay-filter="like-tabs">

            <ul class="layui-tab-title">
                <li lay-id="1" class="layui-this">移动端商城</li>
                <li lay-id="2" style="display: none">PC端商城</li>
            </ul>
            <div class="layui-tab-content" style="padding: 0 15px;">
                <!-- 搜索区域 -->
                <div class="layui-card-body layui-form">

                    <div class="layui-form-item search">

                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: 90px">广告位列表：</label>
                            <div class="layui-input-inline">
                                <select name="pid" id="pid">

                                </select>
                            </div>

                        </div>

                        <div class="layui-inline">
                            <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                            <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 10px">
                    <button class="layui-btn layui-btn-sm layEvent {$view_theme_color}" lay-event="add">新增广告</button>
                </div>

                <table id="like-table-lists" lay-filter="like-table-lists"></table>

                <script type="text/html" id="table-operation">
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                    {{# if(0 == d.status) { }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="status">启用</a>
                    {{# }else{ }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="status">停用</a>
                    {{# } }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                </script>
                <script type="text/html" id="image">
                    <img src="{{d.image}}" style="height:80px;width: 80px" class="image-show">
                </script>

            </div>
        </div>


    </div>
</div>

<script>
    layui.use(["table", "element", "laydate"], function(){
        var table   = layui.table;
        var element = layui.element;
        var form    = layui.form;
        var terminal = 1;

        like.tableLists('#like-table-lists','{:url()}',[
            {field: 'id', width: 60, title: 'ID'}
            ,{field: 'terminal_desc', width: 200, title: '终端'}
            ,{field: 'title', width: 260, align: 'center', title: '广告标题'}
            ,{field: 'image', width: 120,title: '广告图片', templet: '#image'}
            ,{field: 'pname', width: 140, align: 'center',title: '广告位'}
            ,{field: 'link', width: 240, align: 'center', title: '广告链接'}
            ,{field: 'status', width: 120, align: 'center', title: '广告状态', templet: function(d){
                    if(1 == d.status){
                        return '开启';
                    }
                    return  '停用';
                }}
            ,{field: 'sort', width: 120, align: 'center', title: '排序'}
            ,{title: '操作', width: 180, align: 'center', fixed: 'right', toolbar: '#table-operation'}
        ],{terminal:terminal});

        element.on("tab(like-tabs)", function(){
            terminal = this.getAttribute("lay-id");
            table.reload("like-table-lists", {
                where: {terminal: terminal},
                page: { cur: 1 }
            });
            getPositionList();
        });

        getPositionList();
        function getPositionList() {
            like.ajax({
                url: "{:url('decoration.Ad/getPositionList')}",
                data: {terminal: terminal},
                type: "get",
                success: function (res) {
                    if (res.code === 1) {
                        var position_list = res.data;
                        var html = '<option value="">全部</option>';
                        position_list.forEach(function (position,index) {
                            html+='<option value='+position.id+'>'+position.name+'<option>';
                        })
                        $('#pid').html(html);
                        form.render('select');

                    }
                }
            });
        }
        var active = {
            add:function(obj){
                layer.open({
                    type: 2
                    ,title: '新增广告'
                    ,content: '{:url("decoration.Ad/add")}?terminal='+terminal
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'addSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            field['terminal'] = terminal,
                                like.ajax({
                                    url:'{:url("decoration.Ad/add")}',
                                    data:field,
                                    type:"post",
                                    success:function(res)
                                    {
                                        if(res.code == 1) {
                                            layui.layer.msg(res.msg);
                                            layer.close(index);
                                            table.reload('like-table-lists');
                                        }
                                    }
                                });
                        });
                        submit.trigger('click');
                    }
                });

            },
            edit:function(obj){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑广告'
                    ,content: '{:url("decoration.Ad/edit")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'editSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("decoration.Ad/edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload('like-table-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            },
            status: function(obj) {
                var status =   1 == obj.data.status ? 0 : 1;
                if(status){
                    var tips = "确定启用广告:<span style='color: red'>"+ obj.data.title +"</span>";
                }else{
                    var tips = "确定停用广告:<span style='color: red'>"+ obj.data.title +"</span>";
                }
                layer.confirm(tips, function(index) {
                    like.ajax({
                        url: "{:url('decoration.Ad/swtichStatus')}",
                        data: {id: obj.data.id,status:status},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload('like-table-lists');
                            }
                        }
                    });
                    layer.close(index);
                })
            },
            del: function(obj) {
                layer.confirm("确定删除广告:<span style='color: red'>"+ obj.data.title +"</span>", function(index) {
                    like.ajax({
                        url: "{:url('decoration.Ad/del')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            },
        };
        like.eventClick(active);

        form.on("submit(search)", function(data){
            data.field.terminal = terminal;
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(clear-search)", function(){
            $("#pid").val("");
            form.render("select");
            table.reload("like-table-lists", {
                where: {
                    terminal:terminal
                },
                page: {
                    curr: 1
                }
            });
        });
        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,600);
        });


    })
</script>