{layout name="layout1" /}
<style>

</style>
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--操作提示-->
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*管理分销会员的等级，系统默认等级不能删除；</p>
                        <p>*删除分销等级时，会重新调整分销会员等级为系统默认等级，请谨慎操作；</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <!--功能按钮-->
            <div class="btns">
                <buttion class="layui-btn layui-btn-sm layui-bg-blue" id="add">添加分销等级</buttion>
            </div>
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <!--工具条模板-->
            <script type="text/html" id="operate">
                <a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="edit">编辑</a>
                {{#  if(d.is_default != 1){ }}
                <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
                {{#  } }}
            </script>
        </div>
    </div>
</div>


<script>

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).use(['table'], function () {
        let $ = layui.$
            , table = layui.table;

        // 数据表格渲染
        table.render({
            elem: '#lists'
            ,height: 312
            ,url: '{:url("distribution.distribution_level/index")}' //数据接口
            ,method: 'post'
            ,page: false //开启分页
            ,cols: [[ //表头
                {field: 'weights_desc', title: '等级级别', width:150}
                ,{field: 'name', title: '等级名称', width:150}
                ,{field: 'first_ratio', title: '一级佣金比例', width:150}
                ,{field: 'second_ratio', title: '二级佣金比例', width: 150}
                ,{field: 'members_num', title: '分销会员数', width: 150}
                ,{title: '操作', toolbar: '#operate'}
            ]]                    , text: {none: '暂无数据！'}
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        // 工具条事件
        table.on('tool(lists)', function(obj){
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

            if(layEvent === 'del'){ //删除
                layer.confirm('确定要删除吗?', function(index){
                    layer.close(index);
                    like.ajax({
                        url: "{:url('distribution.distribution_level/delete')}",
                        data: {id: obj.data.id},
                        type: "post",
                        success:function(res) {
                            if(res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload("lists");
                            }
                        }
                    });
                });
            } else if(layEvent === 'edit'){ //编辑
                id = obj.data.id;
                // 弹窗显示添加页
                layer.open({
                    type: 2
                    ,title: "编辑分销等级"
                    ,content: "{:url('distribution.distribution_level/edit')}?id=" + id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#editSubmit");
                        iframeWindow.layui.form.on("submit(editSubmit)", function(data){
                            like.ajax({
                                url: "{:url('distribution.distribution_level/edit')}",
                                data: data.field,
                                type: "post",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("lists");
                                    }
                                }
                            });
                            return false;
                        });
                        submit.trigger("click");
                    }
                });
            }
        });

        // 功能按钮
        $('#add').click(function() {
            // 弹窗显示添加页
            layer.open({
                type: 2
                ,title: "添加分销等级"
                ,content: "{:url('distribution.distribution_level/add')}"
                ,area: ["90%", "90%"]
                ,btn: ["确定", "取消"]
                ,yes: function(index, layero){
                    var iframeWindow = window["layui-layer-iframe" + index];
                    var submit = layero.find("iframe").contents().find("#addSubmit");
                    iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                        like.ajax({
                            url: "{:url('distribution.distribution_level/add')}",
                            data: data.field,
                            type: "post",
                            success:function(res) {
                                if(res.code === 1) {
                                    layui.layer.msg(res.msg);
                                    layer.close(index);
                                    table.reload("lists");
                                }
                            }
                        });
                       return false;
                    });
                    submit.trigger("click");
                }
            });
        });


    });


</script>