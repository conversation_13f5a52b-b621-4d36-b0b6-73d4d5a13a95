{layout name="layout1" /}

<style>
    .layui-form-label {
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
    }
    .bank-card-item, .alipay-item {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        transition: all 0.3s;
        cursor: pointer;
    }
    .bank-card-item:hover, .alipay-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .bank-card-item.selected, .alipay-item.selected {
        border-color: #1E9FFF;
        background-color: #f0f9ff;
    }
    .layui-card-header {
        padding: 10px;
        font-weight: bold;
    }
    .layui-card-body {
        padding: 10px;
    }
    .layui-card-body p {
        margin-bottom: 5px;
    }
</style>

<div class="layui-card layui-form" style="box-shadow:none;">
    <div class="layui-card-body">
        <input type="hidden" name="id" value="{$deposit.id}">
        <input type="hidden" name="refund_id" value="{$refund.id}">
        <input type="hidden" name="transfer_voucher" id="transfer_voucher">

        <div class="layui-form-item">
            <label class="layui-form-label">代理信息：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{$user.nickname} (ID: {$user.id})" readonly>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">保证金金额：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{$deposit.amount}" readonly>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>收款方式：</label>
            <div class="layui-input-block">
                <input type="radio" name="payment_type" value="1" title="银行卡" lay-filter="payment-type" {if empty($bank_cards)}disabled{/if}>
                <input type="radio" name="payment_type" value="2" title="支付宝" lay-filter="payment-type" {if empty($alipay_accounts)}disabled{/if}>
            </div>
        </div>

        <!-- 银行卡信息 -->
        <div class="layui-form-item" id="bank-select-container" style="display: none;">
            <label class="layui-form-label"><span style="color:red;">*</span>选择银行卡：</label>
            <div class="layui-input-block">
                {foreach $bank_cards as $bank}
                <div class="layui-card bank-card-item" style="margin-bottom: 10px;">
                    <div class="layui-card-header" style="display: flex; align-items: center;">
                        <input type="radio" name="bank_id" value="{$bank.id}" title="" lay-filter="bank-select" style="margin-right: 10px;">
                        <span style="font-weight: bold;">{$bank.name}</span>
                    </div>
                    <div class="layui-card-body">
                        <p><strong>开户行：</strong>{$bank.branch}</p>
                        <p><strong>开户名：</strong>{$bank.nickname}</p>
                        <p><strong>银行账号：</strong>{$bank.account}</p>
                    </div>
                </div>
                {/foreach}
            </div>
        </div>

        <!-- 支付宝信息 -->
        <div class="layui-form-item" id="alipay-select-container" style="display: none;">
            <label class="layui-form-label"><span style="color:red;">*</span>选择支付宝：</label>
            <div class="layui-input-block">
                {foreach $alipay_accounts as $alipay}
                <div class="layui-card alipay-item" style="margin-bottom: 10px;">
                    <div class="layui-card-header" style="display: flex; align-items: center;">
                        <input type="radio" name="alipay_id" value="{$alipay.id}" title="" lay-filter="alipay-select" style="margin-right: 10px;">
                        <span style="font-weight: bold;">{$alipay.account}</span>
                    </div>
                    <div class="layui-card-body">
                        <p><strong>实名认证：</strong>{$alipay.username}</p>
                    </div>
                </div>
                {/foreach}
            </div>
        </div>

        <!-- 转账凭证上传 -->
        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>转账凭证：</label>
            <div class="layui-input-block">
                <div class="like-upload-image" switch-tab="0" lay-verType="tips">
                    <div class="upload-image-elem"><a class="add-upload-image" id="upload-voucher"> + 添加图片</a></div>
                </div>
                <div class="layui-form-mid layui-word-aux">请上传转账凭证截图，支持jpg、png、gif格式</div>
            </div>
        </div>

        <!-- 备注 -->
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注说明：</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="manualRefundSubmit" id="manualRefundSubmit" value="确认">
        </div>
    </div>
</div>
<script>
    layui.use(['form', 'upload'], function(){
        var form = layui.form;
        var upload = layui.upload;
        var $ = layui.$;

        // 不再需要在 JavaScript 中使用银行卡和支付宝账户数据
        // 因为我们已经在 HTML 中直接显示了这些信息

        // 点击卡片选中对应的单选按钮
        $(document).on('click', '.bank-card-item', function() {
            var radio = $(this).find('input[type="radio"]');
            radio.prop('checked', true);
            form.render('radio');

            // 添加选中样式
            $('.bank-card-item').removeClass('selected');
            $(this).addClass('selected');
        });

        $(document).on('click', '.alipay-item', function() {
            var radio = $(this).find('input[type="radio"]');
            radio.prop('checked', true);
            form.render('radio');

            // 添加选中样式
            $('.alipay-item').removeClass('selected');
            $(this).addClass('selected');
        });

        // 监听收款方式选择
        form.on('radio(payment-type)', function(data){
            // 隐藏所有相关区域
            $('#bank-select-container, #alipay-select-container').hide();

            if(data.value === '1') { // 银行卡
                $('#bank-select-container').show();
                // 如果只有一个银行卡，自动选中
                if($('input[name="bank_id"]').length === 1) {
                    $('input[name="bank_id"]').prop('checked', true);
                    form.render('radio');
                    // 添加选中样式
                    $('.bank-card-item').addClass('selected');
                }
            } else if(data.value === '2') { // 支付宝
                $('#alipay-select-container').show();
                // 如果只有一个支付宝账户，自动选中
                if($('input[name="alipay_id"]').length === 1) {
                    $('input[name="alipay_id"]').prop('checked', true);
                    form.render('radio');
                    // 添加选中样式
                    $('.alipay-item').addClass('selected');
                }
            }
        });

        // 自定义验证规则
        form.verify({
            bankRequired: function(value) {
                var paymentType = $('input[name="payment_type"]:checked').val();
                if(paymentType === '1' && !value) {
                    return '请选择银行卡';
                }
            },
            alipayRequired: function(value) {
                var paymentType = $('input[name="payment_type"]:checked').val();
                if(paymentType === '2' && !value) {
                    return '请选择支付宝账户';
                }
            }
        });

        // 使用项目标准的图片上传方式
        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        });

        // 监听图片上传回调
        window.callback = function(uri) {
            $('#transfer_voucher').val(uri);
            return uri;
        };

        // 表单提交
        form.on('submit(manualRefundSubmit)', function(data){
            var field = data.field;

            // 验证转账凭证
            if(!field.transfer_voucher) {
                layer.msg('请上传转账凭证', {icon: 2});
                return false;
            }

            return true;
        });
    });
</script>
