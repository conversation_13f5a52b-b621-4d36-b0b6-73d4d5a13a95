{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto !important;
        white-space: normal;
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        color: white;
        font-size: 12px;
        font-weight: 500;
    }
    .status-0 { background-color: #FFB800; }
    .status-1 { background-color: #5FB878; }
    .status-2 { background-color: #FF5722; }

    /* 统计卡片样式 */
    .summary-cards {
        margin-bottom: 20px;
    }
    .summary-card {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 1px solid #e6e6e6;
        transition: all 0.3s ease;
    }
    .summary-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }
    .summary-card .layui-card-header {
        border-bottom: 1px solid #f0f0f0;
        font-weight: 500;
        color: #666;
    }
    .summary-card .layui-card-body p {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin: 0;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 操作提示 -->
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*查看代理佣金的结算情况和明细信息。</p>
                        <p>*支持按代理信息、结算状态、时间范围等条件筛选。</p>
                        <p>*可查看佣金的详细计算过程和结算记录。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 佣金汇总 -->
        <div class="layui-card-body">
            <h2 style="margin: 20px 0;">佣金汇总</h2>
            <div class="layui-row layui-col-space15 summary-cards">
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">总佣金</div>
                        <div class="layui-card-body"><p id="total_commission">¥{$statistics.total_commission|default=0}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">已结算佣金</div>
                        <div class="layui-card-body"><p id="settled_commission">¥{$statistics.settled_commission|default=0}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">待结算佣金</div>
                        <div class="layui-card-body"><p id="pending_commission">¥{$statistics.pending_commission|default=0}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">代理数量</div>
                        <div class="layui-card-body"><p id="agent_count">{$statistics.agent_count|default=0}</p></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form" lay-filter="">
                <div class="layui-form-item">
                    <div class="layui-row">
                        <div class="layui-inline">
                            <label class="layui-form-label">代理信息:</label>
                            <div class="layui-input-block">
                                <input type="text" name="keyword" placeholder="代理昵称/编号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">结算状态:</label>
                            <div class="layui-input-block">
                                <select name="status" lay-search="">
                                    <option value="">全部状态</option>
                                    <option value="0">待结算</option>
                                    <option value="1">已结算</option>
                                    <option value="2">已冻结</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">结算时间:</label>
                            <div class="layui-input-inline">
                                <input type="text" id="start_time" name="start_time" class="layui-input" autocomplete="off" placeholder="开始时间">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline">
                                <input type="text" id="end_time" name="end_time" class="layui-input" autocomplete="off" placeholder="结束时间">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button class="layui-btn layui-btn-primary" type="reset">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="commission-lists" lay-filter="commission-lists"></table>

            <!-- 操作列模板 -->
            <script type="text/html" id="table-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail">详情</a>
            </script>
        </div>
    </div>
</div>

<script>
layui.config({
    version: "{$front_version}",
    base: '/static/lib/' //静态资源所在路径
}).use(['form'], function(){
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , element = layui.element
        , laydate = layui.laydate;

    // 日期选择器
    laydate.render({type: "datetime", elem: "#start_time", trigger: "click"});
    laydate.render({type: "datetime", elem: "#end_time", trigger: "click"});

    // 代理信息模板
    var agentInfoTpl = function(d) {
        return '<div style="display: flex; align-items: center;">' +
               '<img src="' + (d.avatar || '__ADMIN_PATH__/images/default_avatar.png') + '" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">' +
               '<div>' +
               '<div style="font-weight: bold;">' + (d.nickname || '未知代理') + '</div>' +
               '<div style="color: #999; font-size: 12px;">编号: ' + (d.agent_sn || '') + '</div>' +
               '</div>' +
               '</div>';
    };

    // 状态模板
    var statusTpl = function(d) {
        var statusText = '';
        var statusClass = '';
        switch(d.status) {
            case 1:
                statusText = '待结算';
                statusClass = 'status-1';
                break;
            case 2:
                statusText = '已结算';
                statusClass = 'status-2';
                break;
            case 3:
                statusText = '已冻结';
                statusClass = 'status-3';
                break;
            case 4:
                statusText = '已退款';
                statusClass = 'status-4';
                break;    
            default:
                statusText = '未知状态';
                statusClass = 'status-0';
        }
        return '<span class="status-badge ' + statusClass + '">' + statusText + '</span>';
    };

    // 渲染表格
    like.tableLists("#commission-lists", "{:url()}", [
        {field: "id", width: 60, title: "ID"}
        , {field: "agent_info", width: 200, align: "center", title: "代理信息", templet: agentInfoTpl}
        , {field: "commission_amount", width: 120, align: "center", title: "佣金金额", templet: function(d){
            return '<span style="color: #FF5722; font-weight: bold;">¥' + (d.commission_amount || 0) + '</span>';
        }}
        , {field: "order_count", width: 100, align: "center", title: "订单数量"}
        , {field: "settlement_period", width: 150, align: "center", title: "结算周期"}
        , {field: "status", width: 100, align: "center", title: "状态", templet: statusTpl}
        , {field: "settlement_time", width: 160, align: "center", title: "结算时间"}
        , {field: "create_time", width: 160, align: "center", title: "创建时间"}
        , {title: "操作", width: 120, align: "center", fixed: "right", toolbar: '#table-operation'}
    ]);

    // 监听搜索
    form.on('submit(search)', function (data) {
        var field = data.field;
        // 执行重载
        table.reload('commission-lists', {
            where: field,
            page: {
                curr: 1
            }
        });
    });

    // 监听工具条
    table.on('tool(commission-lists)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            layer.open({
                type: 2,
                title: '佣金详情',
                shadeClose: true,
                shade: 0.8,
                area: ['80%', '80%'],
                content: '{:url("finance.agent/commissionInfo")}?id=' + data.id
            });
        }
    });
});
</script>
