{layout name="layout1" /}
<style>

</style>
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台管理商圈信息，平台可对信息进行查看，删除等操作</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="keyword" class="layui-form-label">会员信息：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="keyword" name="keyword" autocomplete="off" class="layui-input" placeholder="请输入昵称/编号/手机号">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="content" class="layui-form-label">内容搜索：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="content" name="content" autocomplete="off" class="layui-input" placeholder="请输入种草内容">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="status" class="layui-form-label">审核状态：</label>
                    <div class="layui-input-inline">
                        <select name="status" id="status">
                            <option value="">全部</option>
                            {foreach $status as $item => $val}
                            <option value="{$item}">{$val}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">审核时间:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input time" id="start_time" name="start_time"  autocomplete="off">
                    </div>
                    <div class="layui-input-inline" style="margin-right: 5px;width: 10px;">
                        <label class="layui-form-mid">-</label>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input time" id="end_time" name="end_time"  autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="table-operation">
                {{#  if(d.status == 0){ }}
                <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="audit">审核</a>
                {{#  } }}
                <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="detail">详情</a>
                <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
            </script>
            <script type="text/html" id="table-image">
                {{#  layui.each(d.images, function(index, item){ }}
                <img src="{{item.image}}" style="height:60px;width: 60px;margin-right: 5px;" class="image-show">
                {{#  }); }}
            </script>
            <script type="text/html" id="table-userInfo">
                <img src="{{d.avatar}}" alt="图标" style="width:60px;height:60px;margin-right:5px;">
                <div class="layui-inline" style="text-align:left;">
                    <p>编号：{{d.sn}}</p>
                    <p>昵称：{{d.nickname}}</p>
                </div>
            </script>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form", "laydate"], function(){
        var table   = layui.table;
        var form   = layui.form;
        var laydate   = layui.laydate;

        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
        });

        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
        });

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"user",  align:"center",width: 240, title:"会员信息", templet: "#table-userInfo"}
            ,{field:"content", width: 200,title:"种草内容"}
            ,{field:"images", width: 250, align:"left", title:"图片", templet: "#table-image"}
            ,{field:"like", width: 100, align:"center", title:"点赞数"}
            ,{field:"comment",　width: 100,  align:"center", title:"评论数"}
            ,{field:"status_desc", 　width: 120, align:"center", title:"状态"}
            ,{field:"create_time", width: 180, align:"center", title:"发布时间"}
            ,{title:"操作", align:"left", fixed:"right",width: 220, toolbar:"#table-operation"}
        ]);

        var active = {
            audit: function(obj) {
                layer.open({
                    type: 2
                    ,title: "文章审核"
                    ,content: "{:url('community.CommunityArticle/audit')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('community.CommunityArticle/audit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            detail: function(obj) {
                layer.open({
                    type: 2
                    ,title: "详情"
                    ,content: "{:url('community.CommunityArticle/detail')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,yes: function(index, layero){
                    }
                });
            },
            del: function(obj) {
                var name =  "<div style='width:200px;overflow:hidden;text-overflow: ellipsis;white-space:nowrap;'>" +
                    "<span>确定删除文章：</span>" +
                    "<span style='color: red;'>"+obj.data.content +"</span></div>";

                layer.confirm(name, function(index) {
                    like.ajax({
                        url: "{:url('community.CommunityArticle/del')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            }
        };
        like.eventClick(active);



        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });

        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        form.on("submit(clear-search)", function(){
            $("#keyword").val("");
            $("#content").val("");
            $("#status").val("");
            $("#start_time").val("");
            $("#end_time").val("");
            form.render("select");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });
    })
</script>