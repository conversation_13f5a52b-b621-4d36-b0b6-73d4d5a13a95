<?php

namespace app\admin\validate\setting;

use app\common\basics\Validate;

/**
 * 用户活跃度设置验证器
 * Class UserActivityValidate
 * @package app\admin\validate\setting
 */
class UserActivityValidate extends Validate
{
    protected $rule = [
        // 系统设置
        'system.is_enabled' => 'require|in:0,1',
        'system.login_check_days' => 'require|integer|between:1,30',
        'system.chat_daily_limit' => 'require|integer|between:1,100',
        
        // 积分规则
        'scoring.purchaser_login_score' => 'require|integer|between:0,1000',
        'scoring.publish_demand_score' => 'require|integer|between:0,1000',
        'scoring.chat_score' => 'require|integer|between:0,100',
        'scoring.purchase_score' => 'require|integer|between:0,1000',
        
        // 等级设置
        'levels.level_1_score' => 'require|integer|egt:0',
        'levels.level_2_score' => 'require|integer|egt:0',
        'levels.level_3_score' => 'require|integer|egt:0',
        'levels.level_4_score' => 'require|integer|egt:0',
        'levels.level_5_score' => 'require|integer|egt:0',
    ];

    protected $message = [
        // 系统设置
        'system.is_enabled.require' => '请选择是否启用活跃度系统',
        'system.is_enabled.in' => '启用状态值错误',
        'system.login_check_days.require' => '请输入登录检查天数',
        'system.login_check_days.integer' => '登录检查天数必须为整数',
        'system.login_check_days.between' => '登录检查天数必须在1-30天之间',
        'system.chat_daily_limit.require' => '请输入每日聊天积分上限',
        'system.chat_daily_limit.integer' => '每日聊天积分上限必须为整数',
        'system.chat_daily_limit.between' => '每日聊天积分上限必须在1-100之间',
        
        // 积分规则
        'scoring.purchaser_login_score.require' => '请输入采购商登录积分',
        'scoring.purchaser_login_score.integer' => '采购商登录积分必须为整数',
        'scoring.purchaser_login_score.between' => '采购商登录积分必须在0-1000之间',
        'scoring.publish_demand_score.require' => '请输入发布采购信息积分',
        'scoring.publish_demand_score.integer' => '发布采购信息积分必须为整数',
        'scoring.publish_demand_score.between' => '发布采购信息积分必须在0-1000之间',
        'scoring.chat_score.require' => '请输入聊天积分',
        'scoring.chat_score.integer' => '聊天积分必须为整数',
        'scoring.chat_score.between' => '聊天积分必须在0-100之间',
        'scoring.purchase_score.require' => '请输入购买商品积分',
        'scoring.purchase_score.integer' => '购买商品积分必须为整数',
        'scoring.purchase_score.between' => '购买商品积分必须在0-1000之间',
        
        // 等级设置
        'levels.level_1_score.require' => '请输入1级所需积分',
        'levels.level_1_score.integer' => '1级所需积分必须为整数',
        'levels.level_1_score.egt' => '1级所需积分不能为负数',
        'levels.level_2_score.require' => '请输入2级所需积分',
        'levels.level_2_score.integer' => '2级所需积分必须为整数',
        'levels.level_2_score.egt' => '2级所需积分不能为负数',
        'levels.level_3_score.require' => '请输入3级所需积分',
        'levels.level_3_score.integer' => '3级所需积分必须为整数',
        'levels.level_3_score.egt' => '3级所需积分不能为负数',
        'levels.level_4_score.require' => '请输入4级所需积分',
        'levels.level_4_score.integer' => '4级所需积分必须为整数',
        'levels.level_4_score.egt' => '4级所需积分不能为负数',
        'levels.level_5_score.require' => '请输入5级所需积分',
        'levels.level_5_score.integer' => '5级所需积分必须为整数',
        'levels.level_5_score.egt' => '5级所需积分不能为负数',
    ];

    /**
     * 自定义验证规则：等级积分递增
     * @param array $data
     * @return bool|string
     */
    public function checkLevelProgression($data)
    {
        if (!isset($data['levels'])) {
            return true;
        }
        
        $levels = $data['levels'];
        $scores = [
            $levels['level_1_score'] ?? 0,
            $levels['level_2_score'] ?? 0,
            $levels['level_3_score'] ?? 0,
            $levels['level_4_score'] ?? 0,
            $levels['level_5_score'] ?? 0,
        ];
        
        // 检查是否递增
        for ($i = 1; $i < count($scores); $i++) {
            if ($scores[$i] <= $scores[$i - 1]) {
                return '等级积分必须递增设置（1级 < 2级 < 3级 < 4级 < 5级）';
            }
        }
        
        return true;
    }

    /**
     * 保存配置场景
     * @return UserActivityValidate
     */
    public function sceneSave()
    {
        return $this;
    }
}
