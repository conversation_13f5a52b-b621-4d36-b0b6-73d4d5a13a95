<?php

namespace app\admin\logic\setting;

use app\common\basics\Logic;
use app\common\enum\UserActivityEnum;
use app\common\logic\UserActivityLogic as CommonUserActivityLogic;
use app\common\model\user\UserActivityLog;

/**
 * 用户活跃度设置逻辑类
 * Class UserActivityLogic
 * @package app\admin\logic\setting
 */
class UserActivityLogic extends Logic
{
    /**
     * 获取配置
     * @return array
     */
    public static function getConfig()
    {
        $config = [
            // 系统设置
            'system' => [
                'is_enabled' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_IS_ENABLED),
                'login_check_days' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_LOGIN_CHECK_DAYS),
                'chat_daily_limit' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_CHAT_DAILY_LIMIT),
            ],
            
            // 积分规则
            'scoring' => [
                'purchaser_login_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_PURCHASER_LOGIN_SCORE),
                'publish_demand_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_PUBLISH_DEMAND_SCORE),
                'chat_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_CHAT_SCORE),
                'purchase_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_PURCHASE_SCORE),
            ],
            
            // 等级设置
            'levels' => [
                'level_1_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_1_SCORE),
                'level_2_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_2_SCORE),
                'level_3_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_3_SCORE),
                'level_4_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_4_SCORE),
                'level_5_score' => CommonUserActivityLogic::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_5_SCORE),
            ],
        ];
        
        return $config;
    }

    /**
     * 保存配置
     * @param array $post
     * @return bool
     */
    public static function saveConfig($post)
    {
        try {
            // 保存系统设置
            if (isset($post['system'])) {
                foreach ($post['system'] as $key => $value) {
                    CommonUserActivityLogic::setActivityConfig($key, $value);
                }
            }
            
            // 保存积分规则
            if (isset($post['scoring'])) {
                foreach ($post['scoring'] as $key => $value) {
                    CommonUserActivityLogic::setActivityConfig($key, intval($value));
                }
            }
            
            // 保存等级设置
            if (isset($post['levels'])) {
                foreach ($post['levels'] as $key => $value) {
                    CommonUserActivityLogic::setActivityConfig($key, intval($value));
                }
            }
            
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 获取统计数据
     * @return array
     */
    public static function getStatistics()
    {
        // 用户等级分布
        $level_distribution = [];
        for ($i = 0; $i <= 5; $i++) {
            $count = \app\common\model\user\User::where(['level' => $i, 'del' => 0])->count();
            $level_distribution[] = [
                'level' => $i,
                'level_name' => UserActivityEnum::getLevelName($i),
                'count' => $count,
            ];
        }
        
        // 采购商数量
        $purchaser_count = \app\common\model\user\User::where(['is_purchaser' => 1, 'del' => 0])->count();
        
        // 今日活跃度记录数
        $today_start = strtotime(date('Y-m-d'));
        $today_activity_count = UserActivityLog::where('create_time', '>=', $today_start)->count();
        
        // 各活动类型统计（最近7天）
        $week_start = strtotime('-7 days');
        $activity_stats = [];
        foreach (UserActivityEnum::getAllActivityTypes() as $type) {
            $count = UserActivityLog::where([
                'activity_type' => $type,
                'create_time' => ['>=', $week_start]
            ])->count();
            
            $activity_stats[] = [
                'type' => $type,
                'type_name' => UserActivityEnum::getActivityDesc($type),
                'count' => $count,
            ];
        }
        
        return [
            'level_distribution' => $level_distribution,
            'purchaser_count' => $purchaser_count,
            'today_activity_count' => $today_activity_count,
            'activity_stats' => $activity_stats,
        ];
    }

    /**
     * 获取活跃度日志列表
     * @param array $get
     * @return array
     */
    public static function getActivityLogs($get)
    {
        $where = [];
        
        // 用户ID筛选
        if (!empty($get['user_id'])) {
            $where[] = ['user_id', '=', intval($get['user_id'])];
        }
        
        // 活动类型筛选
        if (!empty($get['activity_type'])) {
            $where[] = ['activity_type', '=', $get['activity_type']];
        }
        
        // 时间范围筛选
        if (!empty($get['start_time'])) {
            $where[] = ['create_time', '>=', strtotime($get['start_time'])];
        }
        if (!empty($get['end_time'])) {
            $where[] = ['create_time', '<=', strtotime($get['end_time']) + 86399];
        }
        
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 20;
        
        $logs = UserActivityLog::with(['user' => function($query) {
            $query->field('id,nickname,mobile');
        }])
        ->where($where)
        ->order('create_time', 'desc')
        ->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);
        
        $list = [];
        foreach ($logs as $log) {
            // 手动构建数据，避免调用获取器
            $item = [
                'id' => $log->id,
                'user_id' => $log->user_id,
                'activity_type' => $log->activity_type,
                'score_change' => $log->score_change,
                'before_score' => $log->before_score,
                'after_score' => $log->after_score,
                'before_level' => $log->before_level,
                'after_level' => $log->after_level,
                'remark' => $log->remark,
                'create_time' => $log->create_time ? date('Y-m-d H:i:s', $log->create_time) : '',
            ];

            // 用户信息
            $item['user_info'] = $log->user ? [
                'id' => $log->user->id,
                'nickname' => $log->user->nickname,
                'mobile' => $log->user->mobile,
            ] : null;

            // 活动类型描述
            $item['activity_type_desc'] = UserActivityEnum::getActivityDesc($log->activity_type);

            // 积分变化文本
            $change = $log->score_change;
            if ($change > 0) {
                $item['score_change_text'] = '+' . $change;
            } elseif ($change < 0) {
                $item['score_change_text'] = $change;
            } else {
                $item['score_change_text'] = '0';
            }

            // 等级变化文本
            $before = UserActivityEnum::getLevelName($log->before_level);
            $after = UserActivityEnum::getLevelName($log->after_level);
            if ($log->before_level != $log->after_level) {
                $item['level_change_text'] = $before . ' → ' . $after;
            } else {
                $item['level_change_text'] = $before;
            }

            $list[] = $item;
        }
        
        return [
            'lists' => $list,
            'count' => $logs->total(),
            'page' => $page,
            'limit' => $limit,
        ];
    }

    /**
     * 重新计算所有用户等级
     * @return bool
     */
    public static function recalculateAllLevels()
    {
        return CommonUserActivityLogic::recalculateAllUserLevels();
    }

    /**
     * 获取配置项说明
     * @return array
     */
    public static function getConfigDescriptions()
    {
        return [
            'system' => [
                'is_enabled' => '是否启用用户活跃度系统',
                'login_check_days' => '采购商登录积分检查天数（天数内重复登录不加分）',
                'chat_daily_limit' => '每日聊天积分获取上限次数',
            ],
            'scoring' => [
                'purchaser_login_score' => '采购商登录获得积分',
                'publish_demand_score' => '发布采购信息获得积分',
                'chat_score' => '用户聊天获得积分',
                'purchase_score' => '购买商品获得积分',
            ],
            'levels' => [
                'level_1_score' => '1级（活跃新手）所需积分',
                'level_2_score' => '2级（活跃用户）所需积分',
                'level_3_score' => '3级（活跃达人）所需积分',
                'level_4_score' => '4级（活跃专家）所需积分',
                'level_5_score' => '5级（活跃大师）所需积分',
            ],
        ];
    }
}
