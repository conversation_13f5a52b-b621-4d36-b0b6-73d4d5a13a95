<?php



namespace app\admin\controller;


use app\admin\logic\LoginLogic;
use app\admin\validate\LoginValidate;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;

class Login extends AdminBase
{
    public $like_not_need_login = ['login'];

    /**
     * Notes: 登录
     * <AUTHOR> 15:08)
     */
    public function login()
    {
        if ($this->request->isAjax()) {
            $post = request()->post();
            (new LoginValidate())->goCheck();
            if (LoginLogic::login($post)){
                return JsonServer::success('登录成功');
            }
            $error = LoginLogic::getError() ?: '登录失败';
            return JsonServer::error($error);
        }

        return view('', [
            'account'  => cookie('account'),
            'config'  => LoginLogic::config(),
        ]);
    }

    /**
     * Notes: 退出登录
     * <AUTHOR> 18:44)
     */
    public function logout()
    {
        LoginLogic::logout();
        $this->redirect(url('login/login'));
    }
}