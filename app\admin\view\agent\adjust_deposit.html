{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">调整代理保证金</div>
        <div class="layui-card-body">
            <!-- 保证金信息区域 -->
            <div class="layui-form-item">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>保证金信息</legend>
                </fieldset>
            </div>

            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">用户信息</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <div class="layui-form-mid">{$user.nickname|default=''} ({$user.sn|default=''}) {$user.mobile|default=''}</div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">保证金金额</label>
                        <div class="layui-input-inline">
                            <div class="layui-form-mid">{$deposit.amount|default='0.00'} 元</div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">当前余额</label>
                        <div class="layui-input-inline">
                            <div class="layui-form-mid">{$deposit.current_balance|default='0.00'} 元</div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <div class="layui-form-mid">
                                {if $deposit.status == 0}未支付
                                {elseif $deposit.status == 1}已支付
                                {elseif $deposit.status == 2}公示期结束(可退)
                                {elseif $deposit.status == 3}退款申请中
                                {elseif $deposit.status == 4}已退款
                                {elseif $deposit.status == 5}退款失败
                                {else}未知状态
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 调整保证金区域 -->
            <div class="layui-form-item">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>调整保证金</legend>
                </fieldset>
            </div>

            <form class="layui-form" action="" lay-filter="adjust-form">
                <input type="hidden" name="deposit_id" value="{$deposit_id}">
                <input type="hidden" name="user_id" value="{$user_id}">

                <div class="layui-form-item">
                    <label class="layui-form-label">变动类型</label>
                    <div class="layui-input-inline" style="width: 200px;">
                        <select name="change_type" lay-verify="required" lay-filter="change_type">
                            <option value="">请选择变动类型</option>
                            <option value="2">增加</option>
                            <option value="3">扣除</option>
                        </select>
                    </div>
                    <div class="layui-form-mid layui-word-aux">增加：为代理增加保证金；扣除：从代理保证金中扣除金额</div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">变动金额</label>
                    <div class="layui-input-inline" style="width: 200px;">
                        <input type="number" name="amount" lay-verify="required|number|gtZero" placeholder="请输入变动金额" autocomplete="off" class="layui-input" min="0.01" step="0.01">
                    </div>
                    <div class="layui-form-mid layui-word-aux">元，请输入大于0的金额</div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">变动原因</label>
                    <div class="layui-input-block">
                        <input type="text" name="reason" lay-verify="required" placeholder="请输入变动原因" autocomplete="off" class="layui-input" style="width: 400px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea" style="width: 400px;"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="adjust-submit">确认调整</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" id="back-btn">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
   layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;

        // 自定义验证规则
        form.verify({
            gtZero: function(value){
                if(value <= 0){
                    return '金额必须大于0';
                }
            }
        });

        // 监听提交
        form.on('submit(adjust-submit)', function(data){
            var field = data.field;

            // 扣除时检查余额是否足够
            if(field.change_type == 3){
                var currentBalance = parseFloat('{$deposit.current_balance}'.replace(/,/g, ''));
                var amount = parseFloat(field.amount);

                if(amount > currentBalance){
                    layer.msg('保证金余额不足，当前余额：' + currentBalance + '元', {icon: 2});
                    return false;
                }
            }

            // 提交表单
            $.ajax({
                url: '{:url("agent.agent/adjustDeposit")}',
                type: 'post',
                data: field,
                success: function(res){
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1, time: 1500}, function(){
                            // 如果是弹窗模式，则关闭弹窗并刷新父页面
                            if ('{$is_popup|default=0}' == '1') {
                                // 获取当前窗口索引
                                var index = parent.layer.getFrameIndex(window.name);
                                // 关闭弹窗
                                parent.layer.close(index);
                                // 刷新父页面表格
                                if (parent.layui && parent.layui.table) {
                                    parent.layui.table.reload('lists');
                                }
                            } else {
                                // 非弹窗模式，跳转到明细页面
                                location.href = '{:url("agent.agent/depositDetails")}?deposit_id={$deposit_id}&user_id={$user_id}';
                            }
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });

            return false;
        });

        // 返回按钮
        $('#back-btn').click(function(){
            // 如果是弹窗模式，则关闭弹窗
            if ('{$is_popup|default=0}' == '1') {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            } else {
                // 非弹窗模式，跳转到明细页面
                location.href = '{:url("agent.agent/depositDetails")}?deposit_id={$deposit_id}&user_id={$user_id}';
            }
        });
    });
</script>


