<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>佣金详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__ADMIN_PATH__/css/layui.css" media="all">
    <link rel="stylesheet" href="__ADMIN_PATH__/css/admin.css" media="all">
    <style>
        .detail-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .detail-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-left: 4px solid #5FB878;
            padding-left: 10px;
        }
        .detail-item {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
        }
        .detail-label {
            width: 120px;
            color: #666;
            font-weight: 500;
        }
        .detail-value {
            flex: 1;
            color: #333;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .status-0 { background-color: #FFB800; }
        .status-1 { background-color: #5FB878; }
        .status-2 { background-color: #FF5722; }
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .amount-highlight {
            color: #FF5722;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- 基本信息 -->
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">基本信息</div>
                <div class="detail-item">
                    <div class="detail-label">佣金ID：</div>
                    <div class="detail-value">{$commission.detal.id|default=''}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">代理信息：</div>
                    <div class="detail-value">
                        <div class="user-info">
                            <img src="{$commission.detal.avatar|default='__ADMIN_PATH__/images/default_avatar.png'}" class="user-avatar">
                            <div>
                                <div style="font-weight: bold; font-size: 16px;">{$commission.detal.nickname|default='未知代理'}</div>
                                <div style="color: #999; margin-top: 5px;">编号: {$commission.detal.agent_sn|default=''}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">佣金金额：</div>
                    <div class="detail-value">
                        <span class="amount-highlight">¥{$commission.detal.money|default=0}</span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">订单总额：</div>
                    <div class="detail-value">¥{$commission.detal.order_total|default=0}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">佣金比例：</div>
                    <div class="detail-value">{$commission.detal.ratio|default=0}%</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">订单类型：</div>
                    <div class="detail-value">
                        {switch name="$commission.detal.order_type|default=0"}
                            {case value="1"}用户集采购会员{/case}
                            {case value="2"}商家入驻费{/case}
                            {case value="3"}商家检验费{/case}
                            {case value="4"}入驻及检验费组合{/case}
                            {default /}未知类型
                        {/switch}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">状态：</div>
                    <div class="detail-value">
                        {switch name="$commission.detal.status|default=0"}
                            {case value="1"}
                                <span class="status-badge status-1">待返佣</span>
                            {/case}
                            {case value="2"}
                                <span class="status-badge status-2">已结算</span>
                            {/case}
                            {case value="3"}
                                <span class="status-badge status-3">失效</span>
                            {/case}
                            {default /}
                                <span class="status-badge status-0">未知状态</span>
                        {/switch}
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间信息 -->
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">时间信息</div>
                <div class="detail-item">
                    <div class="detail-label">创建时间：</div>
                    <div class="detail-value">
                        {$commission.detal.create_time|default='-'}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">结算时间：</div>
                    <div class="detail-value">
                        {$commission.detal.settlement_time|default='-'}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">更新时间：</div>
                    <div class="detail-value">
                        {$commission.detal.update_time|default='-'}
                    </div>
                </div>
            </div>
        </div>

        <!-- 相关订单信息 -->
        {if condition="$commission.detal.order_type == 1 && $commission.jcai"}
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">集采订单信息</div>
                <div class="detail-item">
                    <div class="detail-label">订单编号：</div>
                    <div class="detail-value">{$commission.jcai.sn|default='-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">会员类型：</div>
                    <div class="detail-value">{$commission.jcai.give_type|default='-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">订单金额：</div>
                    <div class="detail-value">¥{$commission.jcai.money|default=0}</div>
                </div>
                {if condition="$commission.jcai_info"}
                <div class="detail-item">
                    <div class="detail-label">购买用户：</div>
                    <div class="detail-value">{$commission.jcai_info.nickname|default='未知用户'}</div>
                </div>
                {/if}
            </div>
        </div>
        {elseif condition="$commission.detal.order_type > 1 && $commission.shop_fees"/}
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">商家费用信息</div>
                <div class="detail-item">
                    <div class="detail-label">费用编号：</div>
                    <div class="detail-value">{$commission.shop_fees.sn|default='-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">费用类型：</div>
                    <div class="detail-value">{$commission.detal.order_type_text|default='-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">费用金额：</div>
                    <div class="detail-value">¥{$commission.shop_fees.money|default=0}</div>
                </div>
                {if condition="$commission.shop_user_info"}
                <div class="detail-item">
                    <div class="detail-label">商家用户：</div>
                    <div class="detail-value">{$commission.shop_user_info.nickname|default='未知用户'}</div>
                </div>
                {/if}
            </div>
        </div>
        {/if}

        <!-- 备注信息 -->
        {if condition="$commission.detal.remark"}
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">备注信息</div>
                <div class="detail-item">
                    <div class="detail-label">备注：</div>
                    <div class="detail-value">{$commission.detal.remark|default='-'}</div>
                </div>
            </div>
        </div>
        {/if}
    </div>
</div>

<script src="__ADMIN_PATH__/layui.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    // 页面加载完成
    console.log('佣金详情页面加载完成');
});
</script>
</body>
</html>
