<?php

namespace app\admin\logic\shop;

use app\common\basics\Logic;
use app\common\model\shop\ShopIconConfig;
use app\common\model\shop\ShopAuth;

/**
 * 移动端图标配置逻辑
 * Class MobileIconLogic
 * @package app\admin\logic\shop
 */
class MobileIconLogic extends Logic
{
    /**
     * Notes: 列表
     * @param array $get
     * @return array
     */
    public static function lists($get = [])
    {
        $where = [];

        // 搜索条件
        if (!empty($get['icon_name'])) {
            $where[] = ['icon_name', 'like', '%' . $get['icon_name'] . '%'];
        }

        if (isset($get['status']) && $get['status'] !== '') {
            $where[] = ['status', '=', $get['status']];
        }

        $model = new ShopIconConfig();
        $result = $model->where($where)
            ->order('sort_order asc, id desc')
            ->paginate([
                'list_rows' => $get['limit'] ?? 15,
                'page' => $get['page'] ?? 1
            ]);

        // 获取权限信息
        $authIds = array_column($result->getCollection()->toArray(), 'auth_id');
        $authList = [];
        if (!empty($authIds)) {
            $authData = ShopAuth::where('id', 'in', $authIds)->column('name', 'id');
            $authList = $authData;
        }

        // 处理数据
        foreach ($result as $k => $item) {
            $result[$k]['auth_name'] = $authList[$item['auth_id']] ?? '未关联';
            $result[$k]['status_text'] = $item['status'] ? '启用' : '禁用';
            $result[$k]['merchant_types_text'] = self::getMerchantTypesText($item['merchant_types']);
        }

        return ['count' => $result->total(), 'lists' => $result->getCollection()];
    }

    /**
     * Notes: 添加
     * @param array $post
     * @return bool
     */
    public static function add($post)
    {
        try {
            // 处理商家类型
            $merchantTypes = self::processMerchantTypes($post['merchant_types'] ?? []);

            $data = [
                'shop_id' => 0, // 0表示全局配置
                'icon_url' => $post['icon_url'] ?? '',
                'icon_name' => $post['icon_name'],
                'icon_path' => $post['icon_path'] ?? '',
                'auth_id' => $post['auth_id'] ?? 0,
                'merchant_types' => $merchantTypes,
                'sort_order' => $post['sort_order'] ?? 50,
                'status' => $post['status'] ?? 1,
                'created_at' => time(),
                'updated_at' => time(),
            ];

            return (new ShopIconConfig())->save($data);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Notes: 编辑
     * @param array $post
     * @return bool
     */
    public static function edit($post)
    {
        try {
            // 处理商家类型
            $merchantTypes = self::processMerchantTypes($post['merchant_types'] ?? []);

            $data = [
                'icon_url' => $post['icon_url'] ?? '',
                'icon_name' => $post['icon_name'],
                'icon_path' => $post['icon_path'] ?? '',
                'auth_id' => $post['auth_id'] ?? 0,
                'merchant_types' => $merchantTypes,
                'sort_order' => $post['sort_order'] ?? 50,
                'status' => $post['status'] ?? 1,
                'updated_at' => time(),
            ];

            return ShopIconConfig::where('id', $post['id'])->update($data);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Notes: 详情
     * @param int $id
     * @return array|null
     */
    public static function detail($id)
    {
        return ShopIconConfig::where('id', $id)->find();
    }

    /**
     * Notes: 删除
     * @param array $ids
     * @return bool
     */
    public static function del($ids)
    {
        try {
            return ShopIconConfig::where('id', 'in', $ids)->delete();
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Notes: 状态切换
     * @param array $post
     * @return bool
     */
    public static function status($post)
    {
        try {
            $data = [
                'status' => $post['disable'] == 0 ? 1 : 0, // 注意这里的逻辑转换
                'updated_at' => time(),
            ];
            return ShopIconConfig::where('id', $post['id'])->update($data);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Notes: 获取商家权限列表
     * @return array
     */
    public static function getShopAuthList()
    {
        return ShopAuth::where(['del' => 0, 'type' => 1])
            ->field('id,name,uri')
            ->order('sort asc, id asc')
            ->select()
            ->toArray();
    }

    /**
     * Notes: 更新排序
     * @param array $post
     * @return bool
     */
    public static function updateSort($post)
    {
        try {
            // 验证排序值
            $sortOrder = intval($post['sort_order']);
            if ($sortOrder < 0) {
                self::$error = '排序值不能小于0';
                return false;
            }

            $data = [
                'sort_order' => $sortOrder,
                'updated_at' => time(),
            ];

            return ShopIconConfig::where('id', $post['id'])->update($data);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Notes: 更新字段
     * @param array $post
     * @return bool
     */
    public static function updateField($post)
    {
        try {
            $allowedFields = ['icon_name', 'icon_path']; // 允许更新的字段
            $field = $post['field'];
            $value = $post['value'];

            // 验证字段是否允许更新
            if (!in_array($field, $allowedFields)) {
                self::$error = '不允许更新该字段';
                return false;
            }

            // 验证值不能为空
            if (trim($value) === '') {
                self::$error = '字段值不能为空';
                return false;
            }

            // 特殊验证
            if ($field === 'icon_name' && mb_strlen($value) > 50) {
                self::$error = '图标名称不能超过50个字符';
                return false;
            }

            if ($field === 'icon_path' && mb_strlen($value) > 200) {
                self::$error = '跳转路径不能超过200个字符';
                return false;
            }

            $data = [
                $field => trim($value),
                'updated_at' => time(),
            ];

            return ShopIconConfig::where('id', $post['id'])->update($data);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Notes: 处理商家类型数据
     * @param array $merchantTypes
     * @return string
     */
    public static function processMerchantTypes($merchantTypes)
    {
        if (empty($merchantTypes)) {
            return '0,1,2'; // 默认全部类型都可访问
        }

        if (is_array($merchantTypes)) {
            return implode(',', $merchantTypes);
        }

        return $merchantTypes;
    }

    /**
     * Notes: 获取商家类型文本
     * @param string $merchantTypes
     * @return string
     */
    public static function getMerchantTypesText($merchantTypes)
    {
        $typeMap = [
            '0' => '0元入驻',
            '1' => '商家会员',
            '2' => '实力厂商'
        ];

        if (empty($merchantTypes)) {
            return '全部';
        }

        $types = explode(',', $merchantTypes);
        $texts = [];
        foreach ($types as $type) {
            if (isset($typeMap[$type])) {
                $texts[] = $typeMap[$type];
            }
        }

        return empty($texts) ? '全部' : implode('、', $texts);
    }

    /**
     * Notes: 获取商家类型选项
     * @return array
     */
    public static function getMerchantTypeOptions()
    {
        return [
            ['value' => '0', 'text' => '0元入驻'],
            ['value' => '1', 'text' => '商家会员'],
            ['value' => '2', 'text' => '实力厂商']
        ];
    }

    /**
     * Notes: 根据商家类型获取图标列表（API接口用）
     * @param int $merchantType 商家类型
     * @return array
     */
    public static function getIconsByMerchantType($merchantType = 0)
    {
        $where = [
            ['status', '=', 1]
        ];

        $result = ShopIconConfig::where($where)
            ->order('sort_order asc, id desc')
            ->select()
            ->toArray();

        // 过滤商家类型
        $filteredResult = [];
        foreach ($result as $item) {
            $allowedTypes = explode(',', $item['merchant_types']);
            if (in_array((string)$merchantType, $allowedTypes)) {
                $filteredResult[] = $item;
            }
        }

        return $filteredResult;
    }
}
